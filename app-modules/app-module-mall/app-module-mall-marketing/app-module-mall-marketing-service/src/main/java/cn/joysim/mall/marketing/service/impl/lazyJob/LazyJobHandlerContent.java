package cn.joysim.mall.marketing.service.impl.lazyJob;


import cn.joysim.common.annotation.LazyJob;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Component
public class LazyJobHandlerContent implements CommandLineRunner {


    @Resource
    private ApplicationContext applicationContext;
    private Map<String, Method> annotatedMethods = new HashMap<>();

    @Override
    public void run(String... args) throws Exception {
        scanAndCacheAnnotatedMethods();
    }


    private void scanAndCacheAnnotatedMethods() {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            Object bean = applicationContext.getBean(beanName);
            //判断beanName是否包含cn.joysim，不包含则跳过
            if (!bean.getClass().getName().contains("cn.joysim.mall")) {
                continue;
            }
            Class<?> clazz = bean.getClass();
            for (Method method : clazz.getDeclaredMethods()) {
                LazyJob annotation = AnnotationUtils.findAnnotation(method, LazyJob.class);
                if (annotation != null) {
                    // 这里假设方法名作为Map的key是唯一的
                    annotatedMethods.put(annotation.value(), method);
                }
            }
        }
    }

    public Map<String, Method> getAnnotatedMethods() {
        return annotatedMethods;
    }


    public Method getByName(String name) {
       return annotatedMethods.get(name);
    }




    
}
