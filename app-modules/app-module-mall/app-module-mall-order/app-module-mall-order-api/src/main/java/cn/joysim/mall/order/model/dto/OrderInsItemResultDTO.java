package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.MarketingBankActivityRecord;
import cn.joysim.mall.order.model.pojo.OrderMain;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/28
 * 下单返回结果
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderInsItemResultDTO implements Serializable {


    private OrderMain orderMain;

    private MarketingBankActivityRecord record;

    private Long paymentOrderId;

}
