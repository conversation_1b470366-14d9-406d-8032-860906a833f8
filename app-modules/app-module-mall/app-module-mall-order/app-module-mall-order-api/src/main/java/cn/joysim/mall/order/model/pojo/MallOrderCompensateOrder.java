package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 补偿与补偿订单
 * @TableName tb_mall_order_compensate_order
 */
@TableName(value ="tb_mall_order_compensate_order")
@Data
public class MallOrderCompensateOrder  implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 补偿的订单ID
     */
    private Long orderId;

    /**
     * 补偿任务的ID
     */
    private Long compensateId;

}