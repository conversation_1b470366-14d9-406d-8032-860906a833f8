package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderActiveState;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单激活记录
 * <AUTHOR>
 * @date 2021/09/01
 */
@Data
@EqualsAndHashCode
@TableName("tb_mall_order_active_record")
public class OrderActiveRecord extends BaseEntity {

    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 激活时间
     */
    private Date activeTime;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * 有效期
     */
    private Date effectiveTime;
    /**
     * 激活状态(0:未激活,1:已激活,2:已过期)
     */
    private OrderActiveState state;
    /**
     * 活动记录Id
     */
    private Long activityRecordId;

}
