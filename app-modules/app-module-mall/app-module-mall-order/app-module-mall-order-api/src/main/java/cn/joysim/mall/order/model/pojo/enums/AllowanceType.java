package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 2.1.1
 * @author: linhs
 * @date: 2020/10/19
 * @desc: 积分类型
 */
public enum AllowanceType implements ValueEnum {
    /**
     * 津贴
     */
    ALLOWANCE(0, "津贴"),
    /**
     * 班点
     */
    BANDIAN(1, "班点"),
    /**
     * 津贴+班点
     */
    ALL(2, "津贴+班点"),
    ;
    @EnumValue
    private Integer code;
    private String text;

    AllowanceType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static AllowanceType fromCode(Integer code) {
        for (AllowanceType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
