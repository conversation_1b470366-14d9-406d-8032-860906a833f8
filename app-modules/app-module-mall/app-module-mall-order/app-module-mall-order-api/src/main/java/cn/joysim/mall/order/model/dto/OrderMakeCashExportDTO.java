package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Data
public class OrderMakeCashExportDTO implements Serializable {

    /**
     * 提现记录Id
     */
    @ExcelProperty(value = "提现记录Id", converter = LongStringConverter.class)
    private Long id;
    /**
     * 用户ID
     */
    @ExcelProperty("用户ID")
    private String userId;
    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String mobile;
    @ExcelProperty("提现类型")
    private String cashTypeStr;
    /**
     * 订单总金额
     */
    @ExcelProperty("提现金额")
    private BigDecimal cashAmount;
    /**
     * 实际到账金额
     */
    @ExcelProperty("提现金额")
    private BigDecimal actArrivalAmount;

    @ExcelProperty("状态")
    private String statusStr;
    /**
     * 微信流水号（微信付款单号）
     */
    @ExcelProperty("微信流水号")
    private String wxDetailId;

    /**
     * 卡券订单号
     */
    @ExcelProperty("卡券订单号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long boxDetailId;

    /**
     * 提现时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "提现时间",converter = ConverterDateTime.class)
    private Date createTime;
    /**
     * 到账时间
     */
    @ExcelProperty("到账时间")
    private String paymentTime;
    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

}
