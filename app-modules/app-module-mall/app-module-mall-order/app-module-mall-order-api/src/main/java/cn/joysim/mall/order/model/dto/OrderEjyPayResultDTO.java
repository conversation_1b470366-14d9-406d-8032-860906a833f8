package cn.joysim.mall.order.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/12
 * @desc: 易加油支付结果
 */
@Data
public class OrderEjyPayResultDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 金额
     */
    BigDecimal amount;

    /**
     * 油号,示例92#
     */
    private String oilCode;

    /**
     * 油枪号
     */
    String oilgunCode;

    /**
     * 油站
     */
    String stationName;
    /**
     * 优惠券（若有使用优惠券）优惠金额，保留两位小数
     */
    private BigDecimal couponAmount;

    /**
     * 使用津贴
     */
    private BigDecimal useAllowance;

    /**
     * 优惠金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 订单总金额，即用户输入金额，保留两位小数
     */
    private String totalAmount;
    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;

}
