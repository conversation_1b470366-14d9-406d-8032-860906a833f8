package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/2
 */
@Data
public class OrderSettleParamDTO {

    /**
     * 结算产品
     */
    private List<OrderSettleSaveDTO> orderSettleSaveDtoList;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 使用的优惠券
     */
    private Map<Long, List<Long>> couponIdMap;
    /**
     * 团购活动Id
     */
    private Long activityId;
    /**
     * 使用津贴
     */
    private Boolean useAllowance;
    /**
     * 是否使用优惠券
     */
    private Boolean useCoupon;
    /**
     * 银行活动id
     **/
    private Long bankActivityId;
    /**
     * 支付类型
     **/
    private OrderPaymentType orderPaymentType;
    /**
     * 云闪付分期数
     **/
    private Integer payChilType;

    /**
     * 银行卡
     */
    private String bankCard;

    /**
     * 银行兑换专区id
     */
    private Long exchangeId;
}
