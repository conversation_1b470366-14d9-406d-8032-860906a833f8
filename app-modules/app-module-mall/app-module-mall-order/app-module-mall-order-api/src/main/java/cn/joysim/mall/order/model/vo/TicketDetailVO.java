package cn.joysim.mall.order.model.vo;

import cn.joysim.mall.order.model.dto.CzfwTicketDetailShopDTO;
import cn.joysim.mall.order.model.pojo.enums.TicketPlatform;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/09/09
 */
@NoArgsConstructor
@Data
public class TicketDetailVO implements Serializable {

    /**
     * 介绍
     */
    private String instructions;
    /**
     * 券码
     */
    private String code;

    private Integer productId;
    /**
     * 电子券产品id
     */
    private Integer eticketProductId;
    /**
     * 描述
     */
    private String description;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 过期时间
     */
    private String expiredTime;
    /**
     * 发送时间
     */
    private String sendTime;
    /**
     * 已使用次数
     */
    private Integer usedTimes;
    /**
     * 面值
     */
    private Integer price;
    /**
     * 可使用次数
     */
    private Integer totalTimes;
    /**
     * 门店Id
     */
    private String shopIds;
    /**
     * 分类
     */
    private Integer categoryValue;
    /**
     * 门店
     */
    private String shops;
    /**
     * 电子券id
     */
    private Integer id;
    /**
     * 企业id
     */
    private Integer enterpriseId;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 分类
     */
    private String category;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 卡券平台
     */
    private TicketPlatform platform;
    /**
     * 微信优惠券Id
     */
    private String couponId;
    /**
     * 微信优惠券批次Id
     */
    private String couponStockId;

    /**
     * 可用门店列表
     */
    private List<CzfwTicketDetailShopDTO> shopList;

}
