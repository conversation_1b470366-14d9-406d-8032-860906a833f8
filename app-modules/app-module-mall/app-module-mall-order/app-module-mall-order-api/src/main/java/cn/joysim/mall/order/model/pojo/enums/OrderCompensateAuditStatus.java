package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
public enum OrderCompensateAuditStatus implements ValueEnum {

    UNDER_REVIEW(0, "待审核"),

    CHECK_REVIEW(1,"审核通过"),

    CHECK_NOT_PASS(2,"审核不通过");

    OrderCompensateAuditStatus(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static OrderCompensateAuditStatus fromCode(Integer code) {
        for (OrderCompensateAuditStatus value : OrderCompensateAuditStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
