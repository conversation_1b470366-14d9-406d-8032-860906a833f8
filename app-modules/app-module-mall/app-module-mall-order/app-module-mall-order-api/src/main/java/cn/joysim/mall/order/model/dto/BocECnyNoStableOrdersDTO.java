package cn.joysim.mall.order.model.dto;

import cn.joysim.common.ecny.configManager.ECnyConfigManager;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingEcnySourceType;
import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BocECnyNoStableOrdersDTO {


    /**
     * 主订单ID
     */
    private Long mainId;

    /**
     * 明细订单ID
     */
    private Long detailId;


    /**
     * 数字人民币活动ID
     */
    private String ecnyActivityId;




    /**
     * 订单状态
     */
    private OrderTicketStateEnum orderState;

    /**
     * 卡券订单状态
     */
    private TicketStateEnum ticketOrderDetailState;


    /**
     * 数币商户类型
     */
    private MarketingEcnySourceType ecnySourceType;


    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 业务订单号
     */
    private Long bizId;

    /**
     * 发券记录id
     */
    private Long sendRecordId;
}
