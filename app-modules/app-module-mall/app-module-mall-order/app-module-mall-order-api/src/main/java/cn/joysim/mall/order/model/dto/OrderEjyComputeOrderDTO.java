package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/03/14
 * @desc 易加油计算订单返回参数
 */
@Data
public class OrderEjyComputeOrderDTO implements Serializable {

    /**
     * 油量，单位升，保留两位小数
     */
    private BigDecimal oilMass;

    /**
     * 用户本单应支付的金额，保留两位小数
     */
    private BigDecimal payAmount;

    /**
     * 服务费，保留两位小数
     */
    private BigDecimal serviceFee;

    /**
     * 直降优惠金额（*****含服务费计算******），保留两位小数，显示返回前端的直降优惠会补回手续费，否则前端会将直降优惠显示成负数
     */
    private BigDecimal discountAmount;

    /**
     * 优惠券（若有使用优惠券）优惠金额，保留两位小数
     */
    private BigDecimal couponAmount;

    /**
     * 最终享受单价
     */
    private String discountPrice;

    /**
     * 国家价
     */
    private String countryPrice;

    /**
     * 挂牌价
     */
    private String stationPrice;

    /**
     * 用户享受单价
     */
    private String payUnitPrice;
}
