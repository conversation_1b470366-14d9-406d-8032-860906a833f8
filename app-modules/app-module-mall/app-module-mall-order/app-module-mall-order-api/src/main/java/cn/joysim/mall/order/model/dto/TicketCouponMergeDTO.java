package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingActivityMyCouponDTO;
import cn.joysim.mall.marketing.model.dto.MarketingActivityMyCouponMiniDTO;
import cn.joysim.mall.order.model.pojo.enums.TicketCouponTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @version: 2.4.4.1
 * @author: linhs
 * @date: 2021/6/11
 * @desc: 卡券、优惠券合并
 */
@Data
public class TicketCouponMergeDTO implements Serializable {


    /**
     * 信用卡专属卡券列表
     */
    List<EsmEticketDTO> creditCards;
    /**
     * 车主服务卡券列表
     */
    List<EsmEticketDTO> czfwTickets;
    /**
     * 其他卡券列表
     */
    List<EsmEticketDTO> otherTickets;

    /**
     * 优惠券列表
     */
    List<EsmEticketDTO> myCoupons;

    /**
     * 全部卡券列表=车主+班马+优惠券
     */
    List<EsmEticketDTO> tickets;

    //组合礼包
    List<EsmEticketDTO> giftPackageTickets;

}
