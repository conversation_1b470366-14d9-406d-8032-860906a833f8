package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 2.3.4
 * @author: linhs
 * @date: 2021/3/2
 * @desc: 提现统计方式
 */
public enum  OrderMakeCashStaType implements ValueEnum {
    /**
     * 本日
     */
    DODAY(0, "本日"),
    /**
     * 本周
     */
    WEEK(1, "本周"),
    /**
     *本月
     */
    MONTH(1, "本月"),
    ;

    OrderMakeCashStaType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }


    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
