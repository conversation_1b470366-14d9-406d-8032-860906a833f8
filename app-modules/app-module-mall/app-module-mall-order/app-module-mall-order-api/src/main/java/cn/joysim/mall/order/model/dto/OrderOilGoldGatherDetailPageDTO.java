package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.user.model.pojo.enums.AllowanceActionType;
import cn.joysim.mall.user.model.pojo.enums.AssetType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/18
 * @desc: 加油金收支明细
 */
@Data
public class OrderOilGoldGatherDetailPageDTO implements Serializable {

    /**
     * 时间
     */
    private String createTime;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 类型
     */
    private AllowanceActionType actionType;
    private String actionTypeStr;

    /**
     * 金额
     */
    private String amount;
    /**
     * 加油金余额
     */
    private String oilGoldSurplus;


    private AssetType assetType;
    /**
     * 变动账户
     */
    private String assetTypeStr;

    /**
     * 总余额
     */
    private BigDecimal totalAmount;
    /**
     * 客户收益
     */
    //private String userIncome;

    /**
     * 班马收益
     */
    ///private BigDecimal bmIncome;
    public String getActionTypeStr() {
        return this.actionType.getText();
    }

    public String getAssetTypeStr() {
        return this.assetType.getText();
    }
}
