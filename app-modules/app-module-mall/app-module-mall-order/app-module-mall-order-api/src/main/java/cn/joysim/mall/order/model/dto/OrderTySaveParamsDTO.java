package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.user.model.pojo.MallUser;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/4
 * @desc: 团油下单接参
 */
@Data
public class OrderTySaveParamsDTO implements Serializable {
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 使用津贴
     */
    private Boolean useAllowance;


    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 用户id
     */
    private Long userId;
}
