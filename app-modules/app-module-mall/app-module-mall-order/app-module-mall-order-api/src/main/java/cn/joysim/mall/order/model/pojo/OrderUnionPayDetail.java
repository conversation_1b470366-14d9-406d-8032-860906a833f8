package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.marketing.model.pojo.enums.YunPayServiceChargeType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @version: 2.4.16
 * @author: linhs
 * @date: 2021/12/24
 * @desc: 银联支付详情（云闪付）
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_union_pay_detail")
public class OrderUnionPayDetail extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;


    /**
     * 限定分期期数
     */
    private Integer limitNum;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 每期手续费(不分期就代表手续费总额)
     */
    private BigDecimal poundage;

    /**
     * 分期价格=分期价是减去优惠金额的总价
     **/
    private BigDecimal stagePrice;

    /**
     * 分期金额=分期价/期数
     */
    private BigDecimal InstallmentAmount;

    /**
     * 景心优惠
     */
    private String joysimPreferential;
    /**
     * 商户贴息
     */
    private String joysimAccrual;

    /**
     * 手续费收取方式
     * 0:一次性
     * 1:分期收取
     **/
    private YunPayServiceChargeType serviceChargeType;

    /**
     * 分期情况
     */
    private String stageDetail;

    /**
     * 首期金额
     */
    private BigDecimal firstStage;


    /**
     * 每期金额
     */
    private BigDecimal eachStage;
    /**
     * 银行卡号
     */
    private String accNo;

    /*******************申码应答时获取***********************/

    /**
     * 第三方平台支付订单号
     */
    private String externalOrderId;

    /**
     * 原交易订单号
     */
    private String dealOrderId;

    /**
     * 00 交易成功
     * 01 交易处理中
     * 03 交易处理失败
     * 05 交易成功 销账 失败(税单使用)
     * 06 已撤销
     * 07 产生退货
     * 08 已全部退货
     * 09 交易关闭
     * 10 退款状态未确定，
     * 需要商户原 退款单号重新发 起(AT 使用)
     * 11 退款转入代发 (AT 使用)
     *
     *
     * 中金的校验状态
     * 10=未支付
     * 20=支付处理中
     * 30=支付成功
     * 40=支付失败
     * 50=订单关闭
     *
     *
     * 零零购的状态
     * 45=交易已受理，请稍后查询交易结果
     */
    private String transSt;
    /**
     * 二维码链接
     */
    private String qrNo;


    /**
     * 二维码图片
     */
    private String qrImage;

    /**
     * 商户保留域
     */
    private String mchntResv;

    /**
     * 设备号
     */
    private String deviceInfo;


    /**
     * 请求流水号
     */
    private String seqId;


    /***********************申码回调时获取*********************/
    /**
     * 回调时银行卡号
     */
    private String notifyAccNo;
    /**
     * 分期信息
     */
    private String installTransInfo;

    /**
     * 银行简写
     */
    private String bankNm;

    /**
     * 0 云闪付 1 非云闪付
     */
    private String appName;

    /**
     * 交易完成时间
     */
    private String traceTime;
    /**
     * 系统跟踪号
     */
    private String traceNo;
    /**
     * 交易金额
     */
    private String transAt;
    /**
     * 订单发送时间
     */
    private String txnTime;
    /**
     * 清算日期
     */
    private String settleDate;
    /**
     * 交易类型，默认 01
     */
    private String transTp;
    /**
     * 优惠金额
     */
    private String discountAmt;


    /**
     * 支付商户号
     */
    private Long mchSettingId;
}
