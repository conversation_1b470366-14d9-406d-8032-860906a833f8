package cn.joysim.mall.order.model.pojo.bm;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderSendState;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 充值加油金赠送记录
 * <AUTHOR>
 * @date 2022/05/17
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_oil_gold_send_record")
public class OrderOilGoldSendRecord extends BaseEntity {

    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 赠送订单Id
     */
    private Long sendOrderId;
    /**
     * 赠送面值
     */
    private BigDecimal amount;
    /**
     * 赠送产品Id
     */
    private Long skuId;
    /**
     * 备注
     */
    private String remark;

    /**
     * 赠送状态
     */
    private OrderSendState sendState;
}
