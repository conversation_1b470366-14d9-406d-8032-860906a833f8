package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.BacktickStringConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateStringConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderWxActivityBillExportDTO implements Serializable {
    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 批次id
     */
    @ExcelProperty(value = "批次id")
    private String batchId;

    /**
     * 券ID
     */
    @ExcelProperty(value = "券ID")
    private String couponId;

    /**
     * 优惠类型
     */
    @ExcelProperty(value = "优惠类型")
    private String discountType;

    /**
     * 优惠金额（核销面额）
     */
    @ExcelProperty(value = "优惠金额（核销面额）")
    private BigDecimal discountAmount;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private BigDecimal orderTotalAmount;

    /**
     * 账单类型（交易类型）0:未知 1:支付 2:退款
     */
    @ExcelProperty(value = "账单类型")
    private String billType;

    /**
     * 支付单号（支付宝交易号）
     */
    @ExcelProperty(value = "支付单号",converter = BacktickStringConverter.class)
    private String paymentOrderNumber;

    /**
     * 消费时间（核销时间）
     */
    @ExcelProperty(value = "消费时间", converter = DateStringConverter.class)
    private Date consumerTime;

    /**
     * 消费商户号
     */
    @ExcelProperty(value = "消费商户号")
    private String consumerBusinessAccount;


    /**
     * 设备号
     */
    @ExcelProperty(value = "设备号")
    private String deviceNumber;

    /**
     * 银行返回流水号
     */
    @ExcelProperty(value = "银行返回流水号",converter = BacktickStringConverter.class)
    private String bankResponseSerialNumber;

    /**
     * 消耗门店编码(微信支付)
     */
    @ExcelProperty(value = "消耗门店编码(微信支付)")
    private String wechatConsumerStoreCode;

    /**
     * 消耗门店编码(商家自有)
     */
    @ExcelProperty(value = "消耗门店编码(商家自有)")
    private String businessConsumerStoreCode;


}
