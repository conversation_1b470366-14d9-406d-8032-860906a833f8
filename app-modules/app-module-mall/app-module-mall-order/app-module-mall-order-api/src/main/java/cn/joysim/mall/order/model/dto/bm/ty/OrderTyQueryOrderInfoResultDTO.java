package cn.joysim.mall.order.model.dto.bm.ty;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/9/13
 * @desc: 团油查询订单详情
 */
@Data
public class OrderTyQueryOrderInfoResultDTO implements Serializable {

    private String orderNo;
    private String gasName;
    private String gasAddress;
    private String orderStatus;
    private String orderTime;
    private String payTime;
    private String oilNo;
    private String oilName;
    private String gunNo;
    private String num;
    private String gunPrice;
    private String userRealPrice;
    private String enterpriseRealPrice;
    private String totalOrderAmount;
    private String totalUserRealAmout;
    private String totalEnterpriseRealAmount;
    private String totalUserDiscountAmount;
    private String totalEnterpriseDiscountAmount;
    private String userId;
    private String source;
}
