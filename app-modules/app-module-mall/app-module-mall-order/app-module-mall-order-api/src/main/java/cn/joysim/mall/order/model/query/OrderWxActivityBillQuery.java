package cn.joysim.mall.order.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/10/26
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderWxActivityBillQuery implements Serializable {

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 批次名称
      */
    private String batchName;

    /**
     * 券ID
     */
    private String couponId;

    /**
     * 优惠类型
     */
    private String discountType;

    /**
     * 消费商户号
     */
    private String consumerBusinessAccount;


//    /**
//     * 活动统计id
//     */
//    private Long activityStatisticsPageId;

//    /**
//     * 活动统计名称
//     */
//    private String activityStatisticsPageName;

    /**
     * 核销开始时间
     */
    @NotNull(message = "核销开始时间不能为空")
    private Date consumerStartTime;

    /**
     * 核销开始时间
     */
    @NotNull(message = "核销结束时间不能为空")
    private Date consumerEndTime;

    /**
     * id,传入时进行每次获取10万数量的限制
     */
    private Long id;


    /**
     * 迁移时间
     */
    private Date migrationTime;
}
