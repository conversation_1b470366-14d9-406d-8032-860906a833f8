package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.RechargeStateEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import cn.joysim.mall.product.model.dto.ProductSKUImgDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderRechargeDetailDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 外部单号
     */
    private String externalOrderId;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 充值金额
     */
    private BigDecimal amount;
    /**
     * 充值状态
     */
    private RechargeStateEnum state;
    /**
     * 充值类型(0：快充，1：慢充，2：娱乐，3：流量)
     */
    private RechargeTypeEnum rechargeType;
    /**
     * 充值结果信息
     */
    private String message;
    /**
     * 提交充值时间
     */
    private Date submitTime;
    /**
     * 充值成功时间
     */
    private Date successTime;
    /**
     * 产品图
     */
    private List<ProductSKUImgDTO> skuImgList;
    /**
     * 结算名称
     */
    private String skuFinalName;
    /**
     * sku名称
     */
    private String skuName;

    /**
     * 充供应商(0：欧飞，1：分销，2：正联)
     */
    private RechargeSupplierEnum rechargeSupplier;
    /**
     * skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 面值
     */
    private BigDecimal faceValue;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 充值结果
     */
    private String result;

    /**
     * 充值记录
     */
    private List<OrderRechargeRecordDTO> rechargeRecordList;


    /**
     * 油卡号
     */
    private String oilCardNo;

    /**
     * 身份证号
     */
    private String idCardNo;
    /**
     * 油卡用户名称
     */
    private String cardUserName;

    /**
     * 油卡id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long oilCardId;
}
