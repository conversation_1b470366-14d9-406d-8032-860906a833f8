package cn.joysim.mall.order.model.dto;

import cn.hutool.core.date.DateUtil;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankType;
import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/14
 */
@Slf4j
@Data
public class HuiZhouBankConfig implements Serializable {

    /**
     * 每个用户日限制数量
     */
    private Integer dayLimit;
    /**
     * 月限制数量
     */
    private Integer monthLimit;
    /**
     * 购买日
     */
    private String buyDay;
    /**
     * 活动每天购买数量
     */
    private Integer dayBuyLimit;
    /**
     * 购买时间点
     */
    private String buyTime;
    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;
    /**
     * 活动产品
     */
    private Long skuId;
    /**
     * 支付银行
     */
    private String paymentBank;

    public boolean canBuy() {
        if (!StringUtils.hasText(buyDay)) {
            return false;
        }

        Date now = new Date();
        int day = DateUtil.dayOfWeek(now);
        String tempBuyDay = "," + this.buyDay + ",";
        return tempBuyDay.contains("," + day + ",");
    }

    public Boolean inBuyTime() {
        if (StringUtils.isEmpty(this.buyTime)) {
            return false;
        }

        String[] split = this.buyTime.split("-");
        if (split.length != 2) {
            return false;
        }

        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        int hour = instance.get(Calendar.HOUR_OF_DAY);
        return hour >= Integer.parseInt(split[0]) && hour < Integer.parseInt(split[1]);
    }

    public MarketingBankType getBankType(MarketingActivityType activityType) {
        MarketingBankType bankType = null;
        if (MarketingActivityType.HZ_RCB_MOVIE_TICKET.equals(activityType)
                || MarketingActivityType.HZ_RCB_SAM_TICKET.equals(activityType)
                || MarketingActivityType.HZ_RCB_CAR_WASH_TICKET.equals(activityType)) {
            // 惠州农商行猫眼电影券、山姆会员券
            bankType = MarketingBankType.HZ_RCB;
        } else if (MarketingActivityType.HZ_ABC_MOVIE_TICKET.equals(activityType)) {
            // 惠州农行猫眼电影券
            bankType = MarketingBankType.HZ_ABC;
        } else {
            log.info("银行信息与充值产品不匹配，银行活动类型：{}，充值产品：{}", activityType.getText(), activityType.getText());
            throw new OrderException(OrderStatusCode.ORDER_BANK_AND_PRODUCT_NOT_MATCH);
        }

        return bankType;
    }

    public boolean paymentBank(String paymentBank) {
        if (StringUtils.isEmpty(paymentBank)) {
            return false;
        }

        List<String> paymentBanks = Arrays.asList(this.paymentBank.split(","));
        return paymentBanks.contains(paymentBank);
    }

}
