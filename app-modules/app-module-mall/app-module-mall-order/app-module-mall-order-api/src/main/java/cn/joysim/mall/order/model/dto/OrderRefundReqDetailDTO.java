package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPayNotifyType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/15
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderRefundReqDetailDTO implements Serializable {

    /**
     * 订单单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 支付单号
     */
    private Long paymentOrderId;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 流水号
     */
    private String seq;

    /**
     * 申请结果内容
     */
    private String result;


    /**
     * 是否退款
     */
    private Boolean refund;

}
