package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.dto.bm.ty.OrderTyCreateOrderParamsDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderOliType;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/14
 * @desc: 团油下单
 */
@Data
public class OrderTySaveDTO implements Serializable {
    /**
     * 团油临时订单参数
     */
    private OrderTyCreateOrderParamsDTO tyCreateOrderParamsDTO;

    /**
     * 手续费
     */
    private BigDecimal serviceFee;
}
