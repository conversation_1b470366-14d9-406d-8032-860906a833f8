package cn.joysim.mall.order.model.pojo;

import cn.joysim.mall.order.model.pojo.enums.OrderPaymentCallBackStateEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 订单支付单据
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_payment_document")
public class OrderPaymentDocument {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 外部订单号
     */
    private String extraOrderId;
    /**
     * 回调状态(0:未回调,1:回调成功,2:回调失败)
     */
    private OrderPaymentCallBackStateEnum state;
    /**
     * 实际支付现金
     */
    private BigDecimal cashAmount;
    /**
     * 支付总金额
     */
    private BigDecimal totalFee;
    /**
     * 微信优惠金额
     */
    private BigDecimal wxCouponFee;
    /**
     * 支付津贴
     */
    private BigDecimal allowanceAmount;
    /**
     * 支付成功时间
     */
    private Date paymentSuccessTime;
    /**
     * 微信支付流水
     */
    private String wxOrderNo;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 微信优惠券Id
     */
    private String wxCouponId;
    /**
     * 支付类型
     */
    private OrderPaymentType paymentType;

    /**
     * 卡种
     */
    private String cardTy;

    /**
     * 银行订单流水号
     */
    private String bankOrderSeq;
}
