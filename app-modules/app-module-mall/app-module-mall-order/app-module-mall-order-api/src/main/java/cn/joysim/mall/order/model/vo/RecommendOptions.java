package cn.joysim.mall.order.model.vo;

import cn.hutool.core.util.NumberUtil;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

@Data
@NoArgsConstructor
public  class RecommendOptions {

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 可优惠￥x.xx
     */
    private BigDecimal slashed;

    public String getAmount() {
        return NumberUtil.decimalFormatMoney(amount.doubleValue());
    }

    public String getSlashed() {
        return Objects.nonNull(slashed)?NumberUtil.decimalFormatMoney(slashed.doubleValue()):null;
    }




    public RecommendOptions(BigDecimal amount) {
        this.amount = amount;
    }
}


