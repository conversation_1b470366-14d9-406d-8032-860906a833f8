package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.1.1
 * @author: linhs
 * @date: 2020/10/19
 * @desc: 积分使用统计
 */
@Data
public class OrderAllowanceUseDTO implements Serializable {

    /**
     * 时间粒度
     */
    private String time;

    /**
     * 消耗积分值
     */
    private String useAllowance;

    /**
     * 积分类型
     */
    private String allowanceType;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 变动后余额
     */
    private String lastAllowance;

    /**
     * 关联订单号
     */
    private String orderId;

    /**
     * 用户ID
     */
    private String userId;
}
