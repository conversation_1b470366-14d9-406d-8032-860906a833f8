package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.jackson.serialize.AddFieldJsonSerializer;
import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import cn.joysim.mall.product.model.dto.ProductSKUImgDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderTicketDetailDTO implements Serializable {

    /**
     * 卡券明细Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;
    /**
     * 面额
     */
    private BigDecimal amount;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 发券状态
     */
    private TicketStateEnum state;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 券码
     */
    private String ticketCode;
    /**
     * 发券结果信息
     */
    private String message;
    /**
     * 产品图
     */
    private List<ProductSKUImgDTO> skuImgList;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 结算名称
     */
    private String skuFinalName;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 电子券类型
     */
    private TicketTypeEnum ticketType;
    /**
     * 产品skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 面值
     */
    private BigDecimal faceValue;


    /**
     * 查看电子券开关
     */
    private Boolean viewTicketOrder;



    /**
     * 车主服务电子券产品id
     */
    private Integer eticketProductId;

    /**
     * 车主服务车主券状态码：
     * 0：正常
     * 1：已作废
     * 2：已消费
     * 3：已过期
     * 4：待激活
     * 5：服务中
     * 6：已升级
     */
    private Integer czfwState;

    /**
     * 车主服务预约地址
     */
    private String subscribeUrl;

    /**
     * 面向客户的发送错误编码
     */
    @JsonSerialize(using = AddFieldJsonSerializer.class)
    private OrderDetailErrorCode errorCode;

    /**
     * 展示补发按钮
     */
    private Boolean showReissueBtn;

    /**
     * esmCode
     */
    private String esmCode;

    /**
     * 充值编码
     */
    private String chargeCode;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 激活码
     */
    private String activateCode;
}
