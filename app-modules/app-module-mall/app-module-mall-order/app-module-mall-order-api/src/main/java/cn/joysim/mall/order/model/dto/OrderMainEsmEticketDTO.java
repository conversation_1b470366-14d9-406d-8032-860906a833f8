package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityCouponDiscountType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityCouponType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.order.model.pojo.enums.TicketPlatform;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderMainEsmEticketDTO  implements Serializable {


    private Long orderId;

    private Long ticketId;

    private TicketStateEnum state;

    private TicketPlatform platform;

    private String thirdCouponId;
}
