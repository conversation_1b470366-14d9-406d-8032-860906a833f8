package cn.joysim.mall.order.model.dto.bm.ty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/10/26
 * @desc: 团油下单前，校验订单
 */
@Data
@Accessors(chain = true)
public class OrderTyCheckOrderDTO implements Serializable {


    /**
     *  油站Id 必填：true
     */
    private String gasId;


    /**
     * 加油金额，单位元，当前下单不能小于10.00 必填：true
     */
    private BigDecimal fuelAmount;


    /**
     * 油号 必填：true
     */
    private Integer oilNo;


    /**
     * 油枪号 必填：true
     */
    private Integer gunNo;


    /**
     * 用户id
     */
    private Long userId;


    /**
     * 报价快照Id 必填：true
     */
    private String snapshotPriceId;


    /**
     * 用户位置经度 必填：true
     */
    private BigDecimal longitude;

    /**
     * 用户位置纬度 必填：true
     */
    private BigDecimal latitude;


    /**
     * 坐标系(可选类型：GCJO2， WGS84，BD09） 其中 GCJO2为⾼德， WGS84 为gps，BD09 为百度 默认为⾼德坐标系 必填:false
     */
    private String userPoiType;

}
