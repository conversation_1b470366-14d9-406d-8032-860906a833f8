package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.AppConst;
import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 券类型
 */
public enum TicketType2RcSpTypeEnum{

    /**
     * 滴滴代驾
     */
    /*DIDI_DRIVING(0, "滴滴代驾"),
    SINOPEC_OIL(1, "石化电子加油券", AppConst.T_SINOPEC_OIL),
    YI_JIE(2, "易捷现金券", AppConst.T_YI_JIE),
    SUPERMARKET(3, "超市卡券", AppConst.T_SUPERMARKET),
    CAR_WASH(4, "洗车券", AppConst.T_CAR_WASH),
    UPDATE_TICKET(5, "升级券", AppConst.T_UPDATE_TICKET),
    ENGINE_OIL_TICKET(6, "机油券", AppConst.T_ENGINE_OIL_TICKET),
    *//**
     * 微信代金券
     *//*
    T_WECHAT(7, "微信代金券",AppConst.T_WECHAT),
    *//**
     * 麦当劳
     *//*
    T_MCD(8, "麦当劳",AppConst.T_MCD),
    *//**
     * 星巴克
     *//*
    T_SBUX(9, "星巴克",AppConst.T_SBUX),
    *//**
     * 天猫券
     *//*
    T_MALL(10, "天猫券",AppConst.T_MALL),
    *//**
     * 京东e卡
     *//*
    T_JD_E(11, "京东e卡",AppConst.T_JD_E),
    *//**
     * 激活微信代金券
     *//*
    T_WECHAT_ACTIVATE(12, "激活微信代金券",AppConst.T_WECHAT_ACTIVATE),

    *//**
     * 支付宝红包
     *//*
    T_ALIPAY_RED_PACKET(13, "支付宝红包",AppConst.T_ALIPAY_RED_PACKET),
    *//**
     * 超市卡券(esm)
     *//*
    //T_SUPER_MARKET(14, "超市卡券(esm)", AppConst.T_SUPER_MARKET),
    *//**
     * 车主服务券(车主平台)
     *//*
    T_CAR_SERVICE(14, "车主服务券(车主平台)", AppConst.T_CAR_SERVICE),

    *//**
     * 其他
     *//*
    T_OTHER(15, "其他", AppConst.T_OTHER),
    *//**
     * 易加油
     *//*
    T_EJY(16, "易加油", AppConst.T_EJY),
    *//**
     * 微信红包
     *//*
    T_WECHAT_RED_PACKET(17, "微信红包", AppConst.T_WECHAT_RED_PACKET),
    *//**
     * 支付宝小程序抽奖红包
     *//*
    T_ALIPAY_CHANNEL_VOUCHER_JMYC(18, "支付宝小程序抽奖红包", AppConst.T_ALIPAY_CHANNEL_VOUCHER_JMYC),

    *//**
     * 系统优惠券
     *//*
    T_SYSTEM_COUPON(19, "系统优惠券", AppConst.T_SYSTEM_COUPON),
    *//**
     * E代驾
     *//*
    T_E_DAI_JIA(20, "E代驾", AppConst.T_E_DAI_JIA),
    *//**
     * 中石化加油卡
     *//*
    T_SINOPEC_OIL_CARD(21, "中石化加油卡", AppConst.T_SINOPEC_OIL_CARD),
    *//**
     * 中石油加油卡
     *//*
    T_CNPC_OIL_CARD(22, "中石油加油卡", AppConst.T_CNPC_OIL_CARD),
    *//**
     * 车主权益包
     *//*
    T_CZFW_PACKAGE(23, "车主权益包", AppConst.T_CZFW_PACKAGE),

    *//**
     * 微信代金券(用户触发)
     *//*
    T_WECHAT_USER_TOUCH(24, "微信代金券(用户触发)", AppConst.T_WECHAT_USER_TOUCH),
    *//**
     * 工银E生活优惠券
     *//*
    T_E_LIFE_COUPON(25, "银行商城优惠券", AppConst.T_E_LIFE_COUPON),

    *//**
     * 千人千券
     *//*
    T_THOUSAND_QUAN(26, "千人千券", AppConst.T_THOUSAND_QUAN),
    *//**
     * 微信代金券esm
     *//*
    T_WECHAT_ESM(27, "微信代金券(esm)",AppConst.T_WECHAT_ESM),

    *//**
     * 支付宝红包(esm)
     *//*
    T_ALIPAY_RED_PACKET_ESM(28, "支付宝红包(esm)",AppConst.T_ALIPAY_RED_PACKET_ESM),

    *//**
     * 云闪付红包(esm)
     *//*
    T_UNION_PAY_RED_PACKET_ESM(29, "云闪付红包(esm)",AppConst.T_UNION_PAY_RED_PACKET_ESM),
    *//**
     * 美团代金券
     *//*
    T_MEI_TUAN_QUAN(30, "美团代金券", AppConst.T_MEI_TUAN_QUAN),

    *//**
     * 微信随机立减金
     *//*
    T_WECHAT_RANDOM(31, "微信随机立减金",AppConst.T_WECHAT_RANDOM),


    *//**
     * 第三方券码
     *//*
    T_THIRD_COUPON(32, "第三方券码", AppConst.T_THIRD_COUPON),

    *//**
     * 中行数字人民币红包
     *//*
    T_E_CNY_RED_PACKET(33,"数字人民币红包",AppConst.T_BOC_E_CNY_RED_PACKET),

    *//**
     * 组合礼包
     *//*
    T_GIFT_PACKAGE(34,"组合礼包",AppConst.T_GIFT_PACKAGE),

    *//**
     * 卡密
     *//*
    T_CARD_CIPHER(35,"卡密",AppConst.T_GIFT_PACKAGE),

    *//**
     * 支付宝红包（选择账户）
     *//*
    T_ALIPAY_RED_PACKET_SELECT_ACCOUNT(36,"支付宝红包（选择账户）",AppConst.T_ALIPAY_RED_PACKET_SELECT_ACCOUNT),*/
    ;



    private TicketSupplierEnum code;

    private Integer spType;


    TicketType2RcSpTypeEnum(TicketSupplierEnum code, Integer spType) {
        this.code = code;
        this.spType = spType;
    }




}
