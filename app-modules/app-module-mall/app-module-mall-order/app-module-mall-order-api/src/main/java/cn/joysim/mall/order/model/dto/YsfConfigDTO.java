package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.4.16
 * @author: linhs
 * @date: 2021/12/28
 * @desc: 云闪付配置
 */
@Data
public class YsfConfigDTO implements Serializable {
    /**
     * 商户id
     */
    private String svcId;
    /**
     * 服务商id
     */
    private String serId;

    /**
     * 申码通知路由
     */
    private String qrNotifyUrl;
    /**
     * 退款通知路由
     */
    private String refundNotifyUrl;
    /**
     * 申码路由
     */
    private String qrCodeUrl;
    /**
     * 查询路由
     */
    private String inQueryUrl;
    /**
     * 退款路由
     */
    private String refundUrl;
    /**
     * 证书路由
     */
    private String cerUrl;
    /**
     * 秘钥路由
     */
    private String pfxUrl;
    /**
     * 秘钥
     */
    private String pwd;


    /**
     * 是否贴息
     */
    private Boolean isDiscount;


}
