package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.vo.MarketingMiniActivityCouponVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/8
 */
@Data
public class OrderRechargeSettleDTO implements Serializable {

    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 用户津贴
     */
    private BigDecimal userAllowance;
    /**
     * 抵扣津贴
     */
    private BigDecimal useAllowance;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount = BigDecimal.ZERO;

    /**
     * 优惠券总金额
     */
    private BigDecimal couponTotalAmount;

    /**
     * 使用银行优惠券
     */
    private List<MarketingMiniActivityCouponVO> useBankCouponList;

}
