package cn.joysim.mall.order.model.query;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/31
 * @desc:
 */
@Data
public class OrderEjyPageQuery implements Serializable {

    public interface ExportGroup {}
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 手机号
     */
    private String phone;



    private String startDate;
    /**
     * 下单结束时间，校验只在导出时生效
     */
    private String endDate;

    /**
     * 订单状态
     */
    private Integer state;

    /**
     * 易加油订单标识
     */
    private String orderSign;

    /**
     * 供应商类型
     * 0：易加油
     * 1：团油
     */
    private Integer supplierType;

}
