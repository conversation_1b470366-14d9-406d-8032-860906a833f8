package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/11/23
 */
@Data
public class OrderPayGoodsDetailDTO implements Serializable {

    /**
     * 产品Id
     */
    private Long skuId;
    /**
     * 优惠后单价，单位（分）
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer quantity;
}
