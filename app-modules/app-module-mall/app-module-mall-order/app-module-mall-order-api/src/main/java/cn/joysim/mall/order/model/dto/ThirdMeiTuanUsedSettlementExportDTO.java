package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:结算单第三方美团导出DTO
 * @Author: LiuHW
 * @date: 2024/5/22 15:51
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ThirdMeiTuanUsedSettlementExportDTO implements Serializable {

    /**
     * 账单类型
     */
    @ExcelProperty(value = "账单类型")
    @ColumnWidth(12)
    private String billType;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    @ColumnWidth(15)
    private String activityId;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    @ColumnWidth(20)
    private String activityName;

    /**
     * 券码
     */
    @ExcelProperty(value = "券码")
    @ColumnWidth(15)
    private String couponCode;

    /**
     * 兑换码
     */
    @ExcelProperty(value = "兑换码")
    @ColumnWidth(15)
    private String redeemCode;

    /**
     * 支付银行
     */
    @ExcelProperty(value = "支付银行")
    @ColumnWidth(25)
    private String paymentBankName;

    /**
     * 支付通道
     */
    @ExcelProperty(value = "支付通道")
    @ColumnWidth(20)
    private String distributionChannel;

    /**
     * 交易成功时间
     */
    @ExcelProperty(value = "交易成功时间", converter = ConverterDateTime.class)
    @ColumnWidth(20)
    private Date transactionSuccessTime;

    /**
     * 支付单金额
     */
    @ExcelProperty(value = "支付单金额")
    @ColumnWidth(15)
    private BigDecimal orderTotalAmount;

    /**
     * 活动优惠金额
     */
    @ExcelProperty(value = "活动优惠金额")
    @ColumnWidth(15)
    private BigDecimal discountAmount;

    /**
     * 银行卡交易金额
     */
    @ExcelProperty(value = "银行卡交易金额")
    @ColumnWidth(15)
    private BigDecimal tradeAmount;

    /**
     * 优惠实际退款金额
     */
    @ExcelProperty(value = "优惠实际退款金额")
    @ColumnWidth(15)
    private BigDecimal refundAmount;

    /**
     * 卡的前六后四位
     */
    @ExcelProperty(value = "卡的前六后四位")
    @ColumnWidth(15)
    private String paymentBankCardNumber;

    /**
     * 请求银行支付的流水号
     */
    @ExcelProperty(value = "请求银行支付的流水号")
    @ColumnWidth(25)
    private String bankRequestSerialNumber;

    /**
     * 银行返回流水号
     */
    @ExcelProperty(value = "银行返回流水号")
    @ColumnWidth(25)
    private String bankResponseSerialNumber;

    /**
     * 银行卡ID
     */
    @ExcelProperty(value = "银行卡ID")
    @ColumnWidth(15)
    private String bankId;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String mobile;

}
