package cn.joysim.mall.order.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
@Accessors(chain = true)
public class WechatCouponQuery implements Serializable {

    /**
     * 小程序appId
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 代金券批次号
     */
    private String couponStockId;
    /**
     * openid
     */
    private String openid;
    /**
     * 代金券id
     */
    private String couponId;
    /**
     * 支付签名Key
     */
    private String paySignKey;
}
