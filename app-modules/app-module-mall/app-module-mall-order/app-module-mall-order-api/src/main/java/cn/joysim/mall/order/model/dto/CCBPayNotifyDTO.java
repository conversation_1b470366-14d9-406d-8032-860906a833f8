package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 建行回调
 * <AUTHOR>
 * @date 2020/8/3
 */
@Data
public class CCBPayNotifyDTO implements Serializable {
    /**
     * 商户柜台代码
     */
    private String POSID;
    /**
     * 分行代码
     */
    private String BRANCHID;
    /**
     * 订单号
     */
    private String ORDERID;
    /**
     * 付款金额
     */
    private BigDecimal PAYMENT;
    /**
     * 币种
     */
    private String CURCODE;
    /**
     * 备注一
     */
    private String REMARK1;
    /**
     * 备注二
     */
    private String REMARK2;
    /**
     * 账号类型
     */
    private String ACC_TYPE;
    /**
     * 成功标志
     */
    private String SUCCESS;
    /**
     * 接口类型
     */
    private String TYPE;
    /**
     * Referer 信息
     */
    private String REFERER;
    /**
     * 客户端ip
     */
    private String CLIENTIP;
    /**
     * 系统记账日期
     */
    private String ACCDATE;
    /**
     * 支付账户信息
     */
    private String USRMSG;
    /**
     * 分期期数
     */
    private String INSTALLNUM;
    /**
     * 客户加密信息
     */
    private String USRINFO;
    /**
     * 错误信息
     */
    private String ERRMSG;
    /**
     * 数字签名
     */
    private String SIGN;

}
