package cn.joysim.mall.order.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.joysim.common.utils.JsonContext;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;

import javax.validation.ValidationException;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/25 14:36
 */
@Data
public class OrderTypeGroupDP implements Serializable {

    /**
     * 订单类型
     */
    private List<OrderTypeEnum> orderTypes;


    /**
     * 卡券类型
     */
    private List<TicketTypeEnum> ticketTypes;


    /**
     * 充值类型
     */
    private List<RechargeTypeEnum> rechargeTypes;



    public OrderTypeGroupDP verify() {
        for (OrderTypeEnum orderType : orderTypes) {
            if (OrderTypeEnum.TICKET_TYPE.equals(orderType)&& CollUtil.isEmpty(ticketTypes)) {
                throw new ValidationException("卡券二级菜单不能为空");
            }
            if (OrderTypeEnum.RECHARGE_TYPE.equals(orderType)&& CollUtil.isEmpty(rechargeTypes)) {
                throw new ValidationException("充值二级菜单不能为空");
            }
        }
        return this;
    }

}
