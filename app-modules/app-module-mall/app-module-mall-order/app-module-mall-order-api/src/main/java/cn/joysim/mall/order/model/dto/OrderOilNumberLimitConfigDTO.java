package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.4.12.4
 * @author: linhs
 * @date: 2021/12/9
 * @desc: 加油券张数限制
 */
@Data
public class OrderOilNumberLimitConfigDTO implements Serializable {

    /**
     * 到账手机号限制
     */
    private Integer mobileLimit;
    /**
     * 下单用户限制
     */
    private Integer userLimit;

    /**
     * 不限制总量sku,逗号分隔
     */
    private String unLimitSkuStr;
}
