package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 列表查询参数
 */
@Data
public class OrderAuditDataQuery {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 手机号
     */
    private String rechargeMobile;
    /**
     * 订单类型
     */
    private OrderAuditDataType auditDataType;


    /**
     * 下单开始时间
     */
    private String startDate;
    /**
     * 下单结束时间
     */
    private String endDate;

    /**
     * 所属企业
     */
    private String enterpriseName;

    /**
     * 支付方式
     * 0：财付通
     * 1：电子券
     * 2：全部
     */
    private Integer payWay;




}
