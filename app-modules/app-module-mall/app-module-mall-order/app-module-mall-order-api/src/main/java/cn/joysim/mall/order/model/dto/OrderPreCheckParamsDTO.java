package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingBankActivityConfigDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/10/7
 * @desc: 前置校验参数
 */
@Data
@Accessors(chain = true)
public class OrderPreCheckParamsDTO implements Serializable {
    private Long activityId;
    private Long userId;
    private MarketingBankActivityConfigDTO config;
    private OrderBankDTO orderBank;
}
