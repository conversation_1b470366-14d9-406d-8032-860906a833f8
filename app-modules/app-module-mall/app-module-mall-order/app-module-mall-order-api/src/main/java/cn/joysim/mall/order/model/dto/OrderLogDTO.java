package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderLogDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;
    /**
     * 操作前状态
     */
    private Integer beforeState;
    /**
     * 操作后状态
     */
    private Integer afterState;
    /**
     * 操作人
     */
    private String operatorMan;
    /**
     * 备注
     */
    private String remark;
    /**
     * 操作时间
     */
    private Date createTime;

}
