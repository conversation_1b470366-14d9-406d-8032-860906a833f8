package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingBankActivityConfigDTO;
import cn.joysim.mall.marketing.model.dto.bank.MarketingBankGatherVehicleFormDTO;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityCardGradeType;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OrderInsBankDTO{

    /**
     * 活动Id
     */
    @NotNull(message = "活动Id不能为空")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long activityId;


    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 校验手机号
     */
    private String validMobile;


    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 充值账号
     */
    private String rechargeMobile;

    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 订单来源
     */
    private OrderSource source;


    /**
     * 验证码
     */
    private String code;

    /**
     * 银行卡
     */
    private String bankCard;



    private MarketingBankActivityConfigDTO config;
    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 车险业务收集客户信息
     */
    private MarketingBankGatherVehicleFormDTO vehicleInfoDTO;


    //NOT SET
    private String customRemark;


    //NOT SET
    private String validMsgCode;


    //NOT SET
    private String userLocation;


    /**
     * 校验内容
     */
    private String qualificationContent;

}
