package cn.joysim.mall.order.model.dto.bm.ty;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.mall.order.model.pojo.enums.OrderTyStateEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/6/28
 * @desc: 团油导出
 */
@Data
public class OrderTyExportResultDTO implements Serializable {
    @ExcelField(title = "订单号", sort = 1)
    private String orderId;

    @ExcelField(title = "支付单号", sort = 2)
    private String paymentOrderId;

    @ExcelField(title = "团油内部订单号", sort = 3)
    private String orderNo;

    @ExcelField(title = "用户现金支付金额（元）", sort = 4)
    private String cashTotalAmount;

    /**
     * 津贴支付总金额
     */
    @ExcelField(title = "用户津贴支付总金额金额（元）",sort = 5)
    private BigDecimal allowanceTotalAmount;


    @ExcelField(title = "优惠总金额（元）", sort = 6)
    private String discountTotalAmount;


    @ExcelField(title = "手机号码", sort = 7)
    private String mobile;

    @ExcelField(title = "下单时间", sort = 8)
    private String orderTime;


    @ExcelField(title = "油站地址", sort = 9)
    private String gasAddress;

    @ExcelField(title = "油站名称", sort = 10)
    private String gasName;

    @ExcelField(title = "油号", sort = 11)
    private String oilNo;

    @ExcelField(title = "油号名称", sort = 12)
    private String oilName;

    @ExcelField(title = "油枪号", sort = 13)
    private String gunNo;

    @ExcelField(title = "加油升数（升）", sort = 14)
    private String num;

    @ExcelField(title = "枪价）", sort = 15)
    private String gunPrice;

    @ExcelField(title = "机显金额（元）", sort = 16)
    private String productTotalAmount;

    @ExcelField(title = "状态", sort = 17)
    private String statusStr;

    @ExcelField(title = "支付方式", sort = 18)
    private String payTypeStr;


    /**
     * 班马手续费
     */
    @ExcelField(title ="班马手续费",sort = 19)
    private BigDecimal bmServiceFee;

    /**
     * 企业总实扣⾦额
     */
    @ExcelField(title ="企业总实扣金额",sort = 20)
    private BigDecimal totalEnterpriseRealAmount;


    /**
     * 订单总金额
     */
    @ExcelField(title ="订单总金额",sort = 21)
    private BigDecimal orderTotalAmount;

    private OrderTyStateEnum tyState;






}
