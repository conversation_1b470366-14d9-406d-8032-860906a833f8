package cn.joysim.mall.order.model.pojo;

import cn.joysim.mall.order.model.pojo.enums.TicketPlatform;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商城购买发送的电子券
 * 发券记录
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_esm_eticket")
public class EsmEticket {

    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 订单详情ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long detailId;

    /**
     * esm券Id
     */
    private Integer ticketId;

    /**
     * 发送批次
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sendBatchId;

    /**
     * 券码
     */
    private String code;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 可使用总数
     */
    private int totalTimes;

    /**
     * 已使用数量
     */
    private int usedTimes;

    /**
     * 0 滴滴权益
     * 2 易捷
     * 6 电子加油券
     * 7 e券-京东e卡
     * 11 e券-天猫享淘券
     * 13 麦当劳电子券
     * 19 e券-猫眼电影券
     * 22 e券-星巴克
     * 27 支付宝立减金券
     * 100 待激活微信代金券
     * 101 系统优惠券
     * 102 E券油卡
     * 103 银行商城优惠券
     * 104 千人千券
     * 105 美团
     * 106 微信随机立减金
     * 108 广东中行数字人民币红包
     * 109 四川中行数字人民币红包
     * 110 工行数字人民币红包
     * 111 充值中心
     * 112 微信红包
     */
    private int refType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 有效期
     */
    private Date expiredTime;

    /**
     * 券类型（发券时没有，后期补上）
     */
    private String category;

    /**
     * 金额（发券时没有，后期补上）
     */
    private String price;
    /**
     * 卡券平台
     */
    private TicketPlatform platform;
    /**
     * 微信优惠券Id
     */
    private String couponId;
    /**
     * 微信优惠券批次Id
     */
    private String couponStockId;
    /**
     * 状态
     * {@link cn.joysim.mall.order.model.pojo.enums.WechatCouponState}
     */
    private Integer state;

    /**
     * 微信代金券开始时间
     */
    private Date wxBeginTime;

    /**
     * 微信代金券结束时间
     */
    private Date wxEndTime;

    /**
     * 微信代金券/支付宝红包发送时间
     */
    private Date wxSendTime;

    /**
     * 微信代金券/支付宝红包使用时间
     */
    private Date wxUseTime;

    /**
     * E代驾券码绑定Id
     */
    private String bindId;


    /**
     * 门槛
     */
    private BigDecimal threshold;

    /**
     * 组合礼包发券记录Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ticketRecordId;

    /**
     * 第三方交易号
     */
    private String transactionNum;

    /**
     * 业务订单号
     */
    private Long bizId;

    /**
     * 第三方活动id
     */
    private String thirdBatchId;

    /**
     * 第三方券码
     */
    private String thirdCouponId;

    /**
     * 核销金额
     */
    private BigDecimal consumeAmount;

}
