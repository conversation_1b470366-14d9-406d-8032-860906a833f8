package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/2/24
 * @desc: 订单失效时间配置（单位是分钟）
 */
@Data
public class OrderTimeExpireConfigDTO implements Serializable {
    /**
     * 实物
     */
    private Integer entityTime;
    /**
     * 卡券
     */
    private Integer ticketTime;
    /**
     * 充值
     */
    private Integer rechargeTime;
    /**
     * 易加油
     */
    private Integer ejyTime;


    public void init() {
        //默认10分钟
        if (entityTime == null) {
            entityTime = 10;
        }
        if (ticketTime == null) {
            ticketTime = 10;
        }
        if (rechargeTime == null) {
            rechargeTime = 10;
        }
        if (ejyTime == null) {
            ejyTime = 10;
        }
        //给微信的时间，至少是5分钟，所以5分钟保底
       /* entityTime=entityTime+5;
        ticketTime=ticketTime+5;
        rechargeTime=rechargeTime+5;*/


    }
}
