package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 2.4.16
 * @author: linhs
 * @date: 2021/12/30
 * @desc: 申码请求参数
 */
@Data
public class OrderYsfQrCodeRequestDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 现金支付总金额
     */
    private BigDecimal cashTotalAmount;


    /**
     * 限定分期期数
     */
    private Integer limitNum;

    /**
     * cn.joysim.mall.marketing.model.pojo.MarketingBankActivityBase#unionPayLockCard为false时，可为空
     * 银行卡号
     */
    private String accNo;


    /**
     * 支付成功跳转返回班马前端URL
     */
    private String frontUrl;

}
