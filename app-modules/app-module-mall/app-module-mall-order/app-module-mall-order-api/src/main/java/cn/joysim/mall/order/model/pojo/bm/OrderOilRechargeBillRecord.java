package cn.joysim.mall.order.model.pojo.bm;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/9/9
 * @desc: 班马加油充值账单
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_oil_recharge_bill_record")
public class OrderOilRechargeBillRecord  extends BaseEntity {
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 外部订单号
     */
    private String externalNo;

    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 对账状态
     */
    private Boolean checkState;

    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;
}
