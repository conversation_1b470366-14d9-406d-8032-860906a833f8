package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单油卡助力记录
 * <AUTHOR>
 * @date 2020/11/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_oil_assistance")
public class OrderOilAssistance extends BaseEntity {

    /**
     * 抢购记录Id
     */
    private Long purchaseRecordId;
    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 助力人数
     */
    private Integer assistanceNum;
    /**
     * 班点
     */
    private BigDecimal banDian;
}
