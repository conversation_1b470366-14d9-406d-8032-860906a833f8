package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.mall.alipay.model.enums.AliPayVoucherAppIdEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/3
 * @desc:
 */
public enum OrderAuditDataType implements ValueEnum {

    /**
     * 电子加油券
     */
    OIL_TICKET(0, "电子加油券"),
    /**
     * 话费
     */
    PHONE_FARE(1, "话费"),


    CAR_WASH(2, "洗美"),

    ENTITY(3, "实物"),
    ;

    OrderAuditDataType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }



    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }


    public static OrderAuditDataType fromText(String text) {
        for (OrderAuditDataType aliPayEnum : OrderAuditDataType.values()) {
            if(aliPayEnum.getText().equals(text)) {
                return aliPayEnum;
            }
        }
        return null;
    }
}


