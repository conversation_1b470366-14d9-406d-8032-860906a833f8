package cn.joysim.mall.order.model.pojo;

import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 主订单
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_main")
public class OrderMain{

    /**
     * 订单号
     */
    @TableId
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 现金支付总金额
     */
    private BigDecimal cashTotalAmount;
    /**
     * 津贴支付总金额
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 总成本
     */
    private BigDecimal totalCost;
    /**
     * 邮费
     */
    private Integer postage;
    /**
     * 订单状态
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderEntityStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderRechargeStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderEjyStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderTyStateEnum}
     */
    private Integer state;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 供应商Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;
    private String supplierName;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 是否完成
     */
    private Boolean finish;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 客服备注
     */
    private String serviceRemark;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 团购Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long groupId;
    /**
     * 订单来源(0:小程序,1:公众号)
     */
    private OrderSource source;

    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 商户号
     */
    private String mchId;
    /**
     * 交易结束时间
     */
    private Date timeExpire;

    /**
     * 是否班马订单
     */
    private Boolean bmOrder;

    /**
     * 赠送单
     */
    private Long handSelOrder;


    /**
     * 订单生效时间
     */
    private Date orderEffectTime;

}
