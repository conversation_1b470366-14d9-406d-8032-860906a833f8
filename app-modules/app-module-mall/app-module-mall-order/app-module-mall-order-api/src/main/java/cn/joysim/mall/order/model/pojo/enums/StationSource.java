package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public enum StationSource implements ValueEnum {

    EJY(0,"易加油"),
    TY(1, "团油");

    StationSource(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static StationSource fromCode(Integer code) {
        for (StationSource value : StationSource.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
