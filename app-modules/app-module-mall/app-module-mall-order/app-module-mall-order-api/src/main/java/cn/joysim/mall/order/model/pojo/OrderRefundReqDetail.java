package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/15
 * @desc: 退款申请
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_refund_req_detail")
public class OrderRefundReqDetail extends BaseEntity {

    /**
     * 订单单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 退款单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long refundId;

    /**
     * 流水号
     */
    private String seq;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 申请结果内容
     */
    private String result;


    /**
     * 是否退款
     */
    private Boolean refund;

}
