package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("tb_mall_order_ejy_merchandise_exe_log")
public class OrderEjyMerchandiseExeLog extends BaseEntity {
    /**
     * 用户手机号码
     */
    private String userPhone;
    /**
     * 优惠券活动id
     */
    private String sourceId;
}
