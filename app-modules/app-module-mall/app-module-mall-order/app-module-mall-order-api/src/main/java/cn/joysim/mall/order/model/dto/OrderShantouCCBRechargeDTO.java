package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 汕头建行充值活动
 * <AUTHOR>
 * @date 2020/11/12
 */
@Data
public class OrderShantouCCBRechargeDTO {

    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 购买sku Id
     */
    private Long skuId;
    /**
     * 校验手机号
     */
    private String mobile;
    /**
     * 银行类型
     */
    private MarketingBankType bankType;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 银行卡号
     */
    private String bankCard;
    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;
    /**
     * 指定日期
     */
    private Boolean special;

    public OrderShantouCCBRechargeDTO() {
        this.special = Boolean.FALSE;
    }
}
