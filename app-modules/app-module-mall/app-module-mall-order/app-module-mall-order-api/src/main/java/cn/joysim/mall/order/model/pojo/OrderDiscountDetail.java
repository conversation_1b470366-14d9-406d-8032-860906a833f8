package cn.joysim.mall.order.model.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 商品优惠明细
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_discount_detail")
public class OrderDiscountDetail{
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 订单明细Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderDetailId;
    /**
     * 订单类型
     */
    private Integer discountType;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 优惠规则Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ruleId;

}
