package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/12/22
 * @desc: 抽奖奖品轮播
 */
@Data
public class OrderLotteryDrawRewardBannerDTO implements Serializable {

        /**
         * 中奖时间
         */
        private Date orderTime;

        /**
         * 中奖手机号
         */
        private String rechargeMobile;

        /**
         * 活动产品名称
         */
        private String activitySkuName;

        /**
         * 用户id
         */
        private String userIdStr;

}
