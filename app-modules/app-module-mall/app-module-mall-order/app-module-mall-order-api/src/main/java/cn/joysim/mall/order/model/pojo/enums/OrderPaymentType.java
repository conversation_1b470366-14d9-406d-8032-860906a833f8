package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.ecny.gongdongBoc.redpacket.enums.DealStateEnum;
import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * 支付类型
 * <AUTHOR>
 * @date 2020/6/3
 */
public enum OrderPaymentType implements ValueEnum {
    /**
     * 支付方式
     */
    WE_CHAT(0, "微信支付"),
    ALLOWANCE(1, "津贴支付"),
    CCB(2, "建行支付"),
    YSF(3,"云闪付"),
    SEND(4, "赠送"),
    /**
     * 0元支付订单
     */
    ACTIVITY_PAY(5, "活动支付"),
    /**
     * 抽奖
     */
    LOTTERY_DRAW(6, "抽奖"),
    /**
     * 班点支付
     */
    BAN_DIAN(7,"班点支付"),

    /**
     * 加油金支付
     */
    OIL_GOLD(8,"加油金支付"),
    /**
     * 银行app支付
     */
    BANK_APP(9,"银行app支付"),
    /**
     * 支付宝支付
     */
    ALIPAY(10,"支付宝支付"),

    /**
     * 班马抽奖
     */
    BM_LOTTERY_DRAW(11,"班马抽奖"),

    /**
     * 补偿
     */
    COMPENSATE(12, "补偿"),


    SIGN_UP(13,"报名")

    ;

    OrderPaymentType(Integer code, String text){
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    @JsonCreator
    public static OrderPaymentType fromCode(Integer code) {
        for (OrderPaymentType value : OrderPaymentType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
