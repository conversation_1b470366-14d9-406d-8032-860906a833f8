package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderInviteType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2020/8/7
 * @desc: 订单任务明细
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_mission_detail")
public class OrderMissionDetail extends BaseEntity {

    /**
     * 下单用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 邀请人用户Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long invitorUserId;
    /**
     * 任务周期Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long cycleId;

    /**
     * 是否被购买任务统计
     */
    private Boolean isSign;

    /**
     * 邀请类型
     * 0：分销邀请
     * 1：任务邀请
     */
    private OrderInviteType inviteType;
}
