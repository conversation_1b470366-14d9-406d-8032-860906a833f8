package cn.joysim.mall.order.model.query;

import cn.joysim.common.model.pojo.enums.LicensePlateType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class OrderInsSummaryQuery {


    @NotNull(message = "保险活动Id不能为空")
    private Long activityId;

    /**
     * 商业险金额
     */
    private BigDecimal businessInsurance;


    /**
     * 驾意险金额
     */
    private BigDecimal drivingAccidentInsurance;



    /**
     * 车牌类型
     */
    @NotNull(message = "车牌类型不能为空")
    private LicensePlateType licensePlateType;

}
