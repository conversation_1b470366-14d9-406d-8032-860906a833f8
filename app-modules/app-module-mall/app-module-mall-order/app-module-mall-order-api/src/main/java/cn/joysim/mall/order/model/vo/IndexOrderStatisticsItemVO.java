package cn.joysim.mall.order.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/1
 */
@Data
public class IndexOrderStatisticsItemVO implements Serializable {

    /**
     * 实物订单
     */
    private List<BigDecimal> entityOrderAmountList;
    /**
     * 话费
     */
    private List<BigDecimal> fareOrderAmountList;
    /**
     * 油卡
     */
    private List<BigDecimal> oilTicketOrderAmountList;
    /**
     * 易捷
     */
    private List<BigDecimal> yiJieTicketOrderAmountList;
    /**
     * 班点
     */
    private List<BigDecimal> banDianOrderAmountList;
    /**
     * 加油金
     */
    private List<BigDecimal> oilGoldOrderAmountList;
    /**
     * 日期
     */
    private List<String> orderTimeList;

    public IndexOrderStatisticsItemVO() {
        entityOrderAmountList = new ArrayList<>(7);
        fareOrderAmountList = new ArrayList<>(7);
        oilTicketOrderAmountList = new ArrayList<>(7);
        yiJieTicketOrderAmountList = new ArrayList<>(7);
        banDianOrderAmountList = new ArrayList<>(7);
        orderTimeList = new ArrayList<>(7);
        oilGoldOrderAmountList = new ArrayList<>(7);
    }
}
