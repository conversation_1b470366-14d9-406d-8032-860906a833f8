package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;
import cn.joysim.mall.order.service.*;
import cn.joysim.mall.order.service.bm.OrderTyDetailService;

/**
 * 订单服务枚举
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public enum OrderDetailServiceType {

    /**
     * 实物订单服务
     */
    ENTITY_ORDER_SERVICE(OrderTypeEnum.ENTITY_TYPE, OrderEntityDetailService.class),
    /**
     * 卡券订单服务
     */
    TICKET_ORDER_SERVICE(OrderTypeEnum.TICKET_TYPE, OrderTicketDetailService.class),
    /**
     * 充值订单服务
     */
    RECHARGE_ORDER_SERVICE(OrderTypeEnum.RECHARGE_TYPE, OrderRechargeDetailService.class),
    /**
     * 易加油订单服务
     */
    EJY_ORDER_SERVICE(OrderTypeEnum.EJY_TYPE, OrderEjyDetailService.class),

    /**
     * 团油订单服务
     */
    TY_ORDER_SERVICE(OrderTypeEnum.TY_TYPE,OrderTyDetailService.class ),

    ;


    private OrderTypeEnum orderType;
    private Class<OrderDetailService> serviceType;

    OrderDetailServiceType(OrderTypeEnum orderType, Class serviceType) {
        this.orderType = orderType;
        this.serviceType = serviceType;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public Class<OrderDetailService> getServiceType() {
        return serviceType;
    }

    public static OrderDetailServiceType fromOrderType(OrderTypeEnum orderType) {

        for (OrderDetailServiceType value : OrderDetailServiceType.values()) {
            if (value.getOrderType().equals(orderType)) {
                return value;
            }
        }

        throw new OrderException(OrderStatusCode.ORDER_TYPE_ERROR);
    }
}
