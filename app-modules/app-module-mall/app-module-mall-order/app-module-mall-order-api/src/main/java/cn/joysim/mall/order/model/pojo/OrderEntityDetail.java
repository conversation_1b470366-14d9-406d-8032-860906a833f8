package cn.joysim.mall.order.model.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 商品订单明细
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_entity_detail")
public class OrderEntityDetail {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;
    /**
     * 产品SkuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 购买价格
     */
    private BigDecimal price;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * sku结算名称
     */
    private String skuFinalName;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

}
