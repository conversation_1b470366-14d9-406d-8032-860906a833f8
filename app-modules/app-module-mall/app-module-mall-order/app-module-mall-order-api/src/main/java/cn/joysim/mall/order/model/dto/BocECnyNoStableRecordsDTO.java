package cn.joysim.mall.order.model.dto;

import cn.joysim.common.ecny.configManager.ECnyConfigManager;
import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingEcnySourceType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingPrizeGrantType;
import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BocECnyNoStableRecordsDTO{

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 卡券订单Id
     */
    private Long orderTicketDetailId;

    /**
     * 活动记录Id
     */
    private Long activityRecordId;

    /**
     * 银行活动id
     */
    private Long bankActivityId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 组合skuId（活动产品的skuId）
     */
    private Long composeSkuId;

    /**
     * 组合sku名称（活动产品的sku名称）
     */
    private String composeSkuName;

    /**
     * 产品Id
     */
    private Long spuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;


    /**
     * 卡券类型类型
     */
    private TicketTypeEnum ticketType;

    /**
     * sku结算名称
     */
    private String skuFinalName;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 面额
     */
    private Integer amount;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 订单状态
     */
    private TicketStateEnum state;

    /**
     * 发券时间
     */
    private Date sendTime;

    /**
     * 充值编码
     */
    private String chargeCode;

    /**
     * 发券结果信息
     */
    private String message;

    /**
     * 发券结果信息
     */
    private String ticketInfo;

    /**
     * 批次Id
     */
    private Long batchId;

    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 面值
     */
    private BigDecimal faceValue;

    /**
     * 销售价
     */
    private BigDecimal sellPrice;

    /**
     * 充供应商
     */
    private TicketSupplierEnum ticketSupplier;

    /**
     * 油卡id
     */
    private Long oilCardId;

    /**
     * openid
     */
    private String openId;

    /**
     * 奖品发放形式,0:直接发放,1:客户自行领取
     */
    private MarketingPrizeGrantType grantType;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 组合礼包sku排序
     */
    private Integer composeSort;

    /**
     * 补发Id
     */
    private Long resentId;


    /**
     * 面向客户的发送卡券错误编码，可空
     * 例如：中行数字人民币"用户未开通钱包"等信息
     * 与发券结果“message”字段的区别与联系：
     * message面向对象是运营，涵盖的信息如:"用户未开通钱包"、"商户微信账户余额不足"、"系统后台配置错误”等
     * errorCode面向对象是用户，涉及到需要"用户未开通钱包"等需要用户参与解决的问题采用此字段告知用户
     */
    private OrderDetailErrorCode errorCode;


    /**
     * 发送电子券时间
     */
    private Date sendTicketTime;

    /**
     * 业务订单号
     */
    private Long bizId;

    /**
     * 组合礼包id
     */
    private Long composeGiftId;

    /**
     * 应用ID
     */
    private String appId;



    /**
     * 数币商户类型
     */
    private MarketingEcnySourceType ecnySourceType;

}
