package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.mall.user.model.pojo.enums.AllowanceActionType;
import cn.joysim.mall.user.model.pojo.enums.AssetType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/18
 * @desc: 加油金收支明细
 */
@Data
public class OrderOilGoldGatherDetailExportDTO implements Serializable {

    /**
     * 时间
     */
    @ExcelField(title = "时间", sort = 1)
    private String createTime;
    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID", sort = 2)
    private String userId;

    /**
     * 手机号
     */
    @ExcelField(title = "手机号", sort = 3)
    private String mobile;

    /**
     * 类型
     */

    private AllowanceActionType actionType;
    @ExcelField(title = "类型", sort = 4)
    private String actionTypeStr;

    /**
     * 金额
     */
    @ExcelField(title = "金额", sort = 5)
    private String amount;


    /**
     * 加油金余额
     */
    @ExcelField(title = "加油金余额", sort = 6)
    private String oilGoldSurplus;
    private AssetType assetType;
    /**
     * 变动账户
     */
    @ExcelField(title = "变动账户", sort = 7)
    private String assetTypeStr;

    /**
     * 客户收益
     */
    //@ExcelField(title = "客户收益", sort = 7)
    //private String userIncome;

    /**
     * 总余额
     */
    @ExcelField(title = "总余额", sort = 8)
    private BigDecimal totalAmount;
    /**
     * 班马收益
     */
    //@ExcelField(title = "班马收益", sort = 8)
    //private String bmIncome;

    public String getActionTypeStr() {
        return this.actionType.getText();
    }
}
