package cn.joysim.mall.order.model.dto;

import cn.joysim.common.util.JSON;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/01
 */
@Data
public class OrderSwitchDTO implements Serializable {

    private List<OrderSwitchItemDTO> orderSwitchList;

    @Data
    public static class OrderSwitchItemDTO implements Serializable {
        /**
         * 订单类型
         */
        private OrderTypeEnum orderType;
        /**
         * 是否关闭
         */
        private Boolean orderSwitch;
    }

    public static void main(String[] args) {
        OrderSwitchDTO orderSwitchDTO = new OrderSwitchDTO();
        List<OrderSwitchItemDTO> orderSwitchList = new ArrayList<>();
        OrderSwitchItemDTO item1 = new OrderSwitchItemDTO();
        item1.setOrderType(OrderTypeEnum.ENTITY_TYPE);
        item1.setOrderSwitch(Boolean.TRUE);
        OrderSwitchItemDTO item2 = new OrderSwitchItemDTO();
        item2.setOrderType(OrderTypeEnum.RECHARGE_TYPE);
        item2.setOrderSwitch(Boolean.TRUE);
        OrderSwitchItemDTO item3 = new OrderSwitchItemDTO();
        item3.setOrderType(OrderTypeEnum.TICKET_TYPE);
        item3.setOrderSwitch(Boolean.TRUE);
        orderSwitchList.add(item1);
        orderSwitchList.add(item2);
        orderSwitchList.add(item3);
        orderSwitchDTO.setOrderSwitchList(orderSwitchList);
        System.out.println(JSON.toJSONString(orderSwitchDTO));
    }
}
