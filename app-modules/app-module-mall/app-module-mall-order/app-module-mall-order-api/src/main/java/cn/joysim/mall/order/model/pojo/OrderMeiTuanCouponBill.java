package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.common.model.pojo.BaseEntity;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/11 14:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_mei_tuan_coupon_bill")
public class OrderMeiTuanCouponBill extends BaseEntity {

    /**
     * 账单类型
     **/
    private String type;

    /**
     * 活动ID
     **/
    private String couponBatchId;


    /**
     * 活动名称
     **/
    private String couponBatchName;

    /**
     * 券码
     **/
    private String code;

    /**
     * 兑换码
     **/
    private String redeemCode;

    /**
     * 支付银行
     **/
    private String bankName;

    /**
     * 支付通道
     **/
    private String payChannelName;

    /**
     * 交易成功时间
     * 账单类型=【支付】时，该字段指：支付成功时间
     * 账单类型=【退款】时，该字段指：退款成功时间
     */
    private Date sucTime;

    /**
     * 支付单金额
     * 账单类型=【退款】时，该字段指原支付单金额
     */
    private String payMoney;

    /**
     * 活动优惠金额
     * 账单类型=【支付】时，该字段指：支付优惠金额
     *
     * 账单类型=【退款】时，该字段指：原活动优惠金额
     */
    private BigDecimal reduceMoney;

    /**
     * 银行卡支付/退款金额
     * 账单类型=【支付】时，该字段指：银行卡支付金额
     *
     * 账单类型=【退款】时，该字段指：银行卡退款金额
     */
    private BigDecimal bankMoney;


    /**
     * 优惠实际支付/退款金额
     **/
    private BigDecimal promMoney;

    /**
     * 卡前六后四位
     **/
    private String first6last4;

    /**
     * 请求银行支付流水号
     **/
    private String reqBankTradeNo;

    /**
     * 银行返回流水号
     **/
    private String repBankTradeNo;

    /**
     * 银行卡ID
     **/
    private String bankcardId;

    /**
     * 权益id
     */
    private String rightsId;

}
