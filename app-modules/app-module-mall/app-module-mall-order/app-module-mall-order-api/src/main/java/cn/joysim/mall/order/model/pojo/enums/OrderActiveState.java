package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 订单激活状态
 * <AUTHOR>
 * @date 2021/09/01
 */
public enum OrderActiveState implements ValueEnum {

    /**
     * 未激活
     */
    UN_ACTIVE(0, "未激活"),
    ACTIVE(1, "已激活"),
    EXPIRE(2, "已过期");

    @EnumValue
    private Integer code;
    private String text;

    OrderActiveState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
