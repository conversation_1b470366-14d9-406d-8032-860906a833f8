package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.AppConst;
import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.common.model.pojo.enums.ProductCategorySettleType;
import com.baomidou.mybatisplus.annotation.EnumValue;

import java.util.ArrayList;
import java.util.List;

/**
 * 券类型
 */
public enum TicketTypeEnum implements ValueEnum {

    /**
     * 滴滴代驾
     */
    DIDI_DRIVING(0, "滴滴代驾", AppConst.T_DIDI_DRIVING, ProductCategorySettleType.DIDI_DRIVING, false),
    SINOPEC_OIL(1, "石化电子加油券", AppConst.T_SINOPEC_OIL, ProductCategorySettleType.SINOPEC_OIL, false),
    YI_JIE(2, "易捷现金券", AppConst.T_YI_JIE, ProductCategorySettleType.YI_JIE, false),
    SUPERMARKET(3, "超市卡券", AppConst.T_SUPERMARKET, ProductCategorySettleType.SUPERMARKET, false),
    CAR_WASH(4, "洗车券", AppConst.T_CAR_WASH, ProductCategorySettleType.CAR_WASH, false),
    UPDATE_TICKET(5, "升级券", AppConst.T_UPDATE_TICKET, ProductCategorySettleType.UPDATE_TICKET, false),
    ENGINE_OIL_TICKET(6, "机油券", AppConst.T_ENGINE_OIL_TICKET, ProductCategorySettleType.ENGINE_OIL_TICKET, false),
    /**
     * 微信代金券
     */
    T_WECHAT(7, "微信代金券", AppConst.T_WECHAT, ProductCategorySettleType.T_WECHAT, true),
    /**
     * 麦当劳
     */
    T_MCD(8, "麦当劳", AppConst.T_MCD, ProductCategorySettleType.T_MCD, false),
    /**
     * 星巴克
     */
    T_SBUX(9, "星巴克", AppConst.T_SBUX, ProductCategorySettleType.T_SBUX, false),
    /**
     * 天猫券
     */
    T_MALL(10, "天猫券", AppConst.T_MALL, ProductCategorySettleType.T_MALL, false),
    /**
     * 京东e卡
     */
    T_JD_E(11, "京东e卡", AppConst.T_JD_E, ProductCategorySettleType.T_JD_E, false),
    /**
     * 激活微信代金券
     */
    T_WECHAT_ACTIVATE(12, "激活微信代金券", AppConst.T_WECHAT_ACTIVATE, ProductCategorySettleType.T_WECHAT_ACTIVATE, true),

    /**
     * 支付宝红包
     */
    T_ALIPAY_RED_PACKET(13, "支付宝红包(废弃)", AppConst.T_ALIPAY_RED_PACKET, ProductCategorySettleType.T_ALIPAY_RED_PACKET, true),
    /**
     * 超市卡券(esm)
     */
    //T_SUPER_MARKET(14, "超市卡券(esm)", AppConst.T_SUPER_MARKET,//T_SUPER_MARKET(14, "超市卡券(esm)", AppConst.T_SUPER_MARKET,ProductCategoryType.),ProductCategoryType.),
    /**
     * 车主服务券(车主平台)
     */
    T_CAR_SERVICE(14, "车主服务券(车主平台)", AppConst.T_CAR_SERVICE, ProductCategorySettleType.T_CAR_SERVICE, false),

    /**
     * 其他
     */
    T_OTHER(15, "其他", AppConst.T_OTHER, ProductCategorySettleType.T_OTHER, false),
    /**
     * 易加油
     */
    T_EJY(16, "易加油", AppConst.T_EJY, ProductCategorySettleType.T_EJY, false),
    /**
     * 微信红包
     */
    T_WECHAT_RED_PACKET(17, "微信红包", AppConst.T_WECHAT_RED_PACKET, ProductCategorySettleType.T_WECHAT_RED_PACKET, false),
    /**
     * 支付宝小程序抽奖红包
     */
    T_ALIPAY_CHANNEL_VOUCHER_JMYC(18, "支付宝小程序抽奖红包", AppConst.T_ALIPAY_CHANNEL_VOUCHER_JMYC, ProductCategorySettleType.T_ALIPAY_CHANNEL_VOUCHER_JMYC, true),

    /**
     * 系统优惠券
     */
    T_SYSTEM_COUPON(19, "系统优惠券", AppConst.T_SYSTEM_COUPON, ProductCategorySettleType.T_SYSTEM_COUPON, false),
    /**
     * E代驾
     */
    T_E_DAI_JIA(20, "E代驾", AppConst.T_E_DAI_JIA, ProductCategorySettleType.T_E_DAI_JIA, false),
    /**
     * 中石化加油卡
     */
    T_SINOPEC_OIL_CARD(21, "中石化加油卡", AppConst.T_SINOPEC_OIL_CARD, ProductCategorySettleType.T_SINOPEC_OIL_CARD, false),
    /**
     * 中石油加油卡
     */
    T_CNPC_OIL_CARD(22, "中石油加油卡", AppConst.T_CNPC_OIL_CARD, ProductCategorySettleType.T_CNPC_OIL_CARD, false),
    /**
     * 车主权益包
     */
    T_CZFW_PACKAGE(23, "车主权益包", AppConst.T_CZFW_PACKAGE, ProductCategorySettleType.T_CZFW_PACKAGE, false),

    /**
     * 微信代金券(用户触发)
     */
    T_WECHAT_USER_TOUCH(24, "微信代金券(用户触发)", AppConst.T_WECHAT_USER_TOUCH, ProductCategorySettleType.T_WECHAT_USER_TOUCH, true),
    /**
     * 工银E生活优惠券
     */
    T_E_LIFE_COUPON(25, "银行商城优惠券", AppConst.T_E_LIFE_COUPON, ProductCategorySettleType.T_E_LIFE_COUPON, false),

    /**
     * 千人千券
     */
    T_THOUSAND_QUAN(26, "千人千券", AppConst.T_THOUSAND_QUAN, ProductCategorySettleType.T_THOUSAND_QUAN, false),
    /**
     * 微信代金券esm
     */
    T_WECHAT_ESM(27, "微信代金券(esm)", AppConst.T_WECHAT_ESM, ProductCategorySettleType.T_WECHAT_ESM, true),

    /**
     * 支付宝红包(esm)
     */
    T_ALIPAY_RED_PACKET_ESM(28, "支付宝红包(esm)", AppConst.T_ALIPAY_RED_PACKET_ESM, ProductCategorySettleType.T_ALIPAY_RED_PACKET_ESM, true),

    /**
     * 云闪付红包(esm)
     */
    T_UNION_PAY_RED_PACKET_ESM(29, "云闪付红包(esm)", AppConst.T_UNION_PAY_RED_PACKET_ESM, ProductCategorySettleType.T_UNION_PAY_RED_PACKET_ESM, true),
    /**
     * 美团代金券
     */
    T_MEI_TUAN_QUAN(30, "美团代金券", AppConst.T_MEI_TUAN_QUAN, ProductCategorySettleType.T_MEI_TUAN_QUAN, true),

    /**
     * 微信随机立减金
     */
    T_WECHAT_RANDOM(31, "微信随机立减金", AppConst.T_WECHAT_RANDOM, ProductCategorySettleType.T_WECHAT_RANDOM, true),


    /**
     * 第三方券码
     */
    T_THIRD_COUPON(32, "第三方券码", AppConst.T_THIRD_COUPON, ProductCategorySettleType.T_THIRD_COUPON, false),

    /**
     * 中行数字人民币红包
     */
    T_E_CNY_RED_PACKET(33, "数字人民币红包", AppConst.T_BOC_E_CNY_RED_PACKET, ProductCategorySettleType.T_E_CNY_RED_PACKET, false),

    /**
     * 组合礼包
     * 内容不确定，所以是false
     */
    T_GIFT_PACKAGE(34, "组合礼包", AppConst.T_GIFT_PACKAGE, ProductCategorySettleType.T_GIFT_PACKAGE, null),

    /**
     * 卡密
     */
    T_CARD_CIPHER(35, "卡密", AppConst.T_GIFT_PACKAGE, ProductCategorySettleType.T_CARD_CIPHER, false),

    /**
     * 支付宝红包（选择账户）
     */
    T_ALIPAY_RED_PACKET_SELECT_ACCOUNT(36, "支付宝红包(选择账户)", AppConst.T_ALIPAY_RED_PACKET_SELECT_ACCOUNT, ProductCategorySettleType.T_ALIPAY_RED_PACKET_SELECT_ACCOUNT, true),

    ;

    TicketTypeEnum(Integer code, String text, String categoryCode, ProductCategorySettleType categoryCodeType, Boolean thirdBillSettle) {
        this.code = code;
        this.text = text;
        this.categoryCode = categoryCode;
        this.thirdBillSettle = thirdBillSettle;
    }

    public static TicketTypeEnum fromCode(Integer code) {
        for (TicketTypeEnum orderTypeEnum : TicketTypeEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    public static TicketTypeEnum fromCategoryCode(String categoryCode) {
        for (TicketTypeEnum orderTypeEnum : TicketTypeEnum.values()) {
            if (orderTypeEnum.getCategoryCode().equals(categoryCode)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    /**
     * 第三方账单结算卡券类型
     *
     * @param
     * @return java.util.List<java.lang.Integer>
     * <AUTHOR>
     * @date 2024/6/5
     */
    public static List<Integer> getThirdBillSettleTicketType() {

        List<Integer> thirdBillSettleTicketTypeList = new ArrayList<>();

        for (TicketTypeEnum orderTypeEnum : TicketTypeEnum.values()) {
            if (orderTypeEnum.getThirdBillSettle() != null && orderTypeEnum.getThirdBillSettle()) {
                thirdBillSettleTicketTypeList.add(orderTypeEnum.getCode());
            }
        }
        return thirdBillSettleTicketTypeList;
    }

    /**
     * 非第三方账单结算卡券类型
     *
     * @param
     * @return java.util.List<java.lang.Integer>
     * <AUTHOR>
     * @date 2024/6/5
     */
    public static List<Integer> getNonThirdBillSettleTicketType() {

        List<Integer> thirdBillSettleTicketTypeList = new ArrayList<>();

        for (TicketTypeEnum orderTypeEnum : TicketTypeEnum.values()) {
            if (orderTypeEnum.getThirdBillSettle() != null && !orderTypeEnum.getThirdBillSettle()) {
                thirdBillSettleTicketTypeList.add(orderTypeEnum.getCode());
            }
        }

        //移除其他
        thirdBillSettleTicketTypeList.remove(T_OTHER.getCode());
        //thirdBillSettleTicketTypeList.remove(T_WECHAT_RED_PACKET.getCode());
        return thirdBillSettleTicketTypeList;
    }

    /**
     * 非第三方没有esm记录（其他）
     * @param
     * @return java.util.List<java.lang.Integer>
     * <AUTHOR>
     * @date 2024/6/24
     */
    public static List<Integer> getNonThirdOtherBillSettleTicketType() {

        List<Integer> thirdBillSettleTicketTypeList = new ArrayList<>();

        thirdBillSettleTicketTypeList.add(T_OTHER.getCode());
        //thirdBillSettleTicketTypeList.add(T_WECHAT_RED_PACKET.getCode());


        return thirdBillSettleTicketTypeList;
    }

    @EnumValue
    private Integer code;
    private String text;
    private String categoryCode;
    private Boolean thirdBillSettle;

    private String categoryCodeType;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public String getCategoryCodeType() {
        return categoryCodeType;
    }

    public Boolean getThirdBillSettle() {
        return thirdBillSettle;
    }

}
