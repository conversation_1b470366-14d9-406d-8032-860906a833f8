package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 实物订单状态
 */
public enum OrderEntityStateEnum implements ValueEnum {

    /**
     * 已取消
     */
    CANCEL(-1, "已取消"),
    /**
     * 未支付
     */
    UN_PAY(0, "未支付"),
    /**
     * 待发货
     */
    UN_SEND(1, "待发货"),
    /**
     * 已发货
     */
    SEND(2, "已发货"),
    /**
     * 已签收
     */
    SIGN_IN(3, "已签收"),
    /**
     * 已退款
     */
    REFUNDED(4, "已退款"),
    /**
     * 拼团中
     */
    SPELLING(5, "拼团中"),
    /**
     * 退款中
     */
    REFUNDING(6, "退款中"),

    /**
     * 已支付
     */
    PAYED(7, "已支付"),

    /**
     * 退款失败
     */
    REFUND_FAIL(8, "退款失败"),
    /**
     * 待领取（缺少收货地址）
     */
    USER_UN_RECEIVE(9, "待领取"),
    /**
     * 未领取已过期
     */
    UN_RECEIVE_EXPIRE(10, "未领取已过期"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEntityStateEnum fromCode(Integer code) {
        for (OrderEntityStateEnum stateEnum : OrderEntityStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEntityStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
