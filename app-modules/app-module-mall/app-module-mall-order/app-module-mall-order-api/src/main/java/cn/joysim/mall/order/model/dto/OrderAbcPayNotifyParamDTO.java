package cn.joysim.mall.order.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/7/14
 * @desc: 农行回调参数
 */
@Data
@Accessors(chain = true)
public class OrderAbcPayNotifyParamDTO implements Serializable {
    /**
     * 支付单号
     */
    private Long paymentOrderId;

    //支付面额，需要转化为分
    private BigDecimal amount;

    /**
     * 流水号
     */
    private String iRspRef;
}
