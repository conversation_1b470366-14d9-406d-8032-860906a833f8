package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPayNotifyType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/15
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderPayCallBackNotifyDetailDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 回调内容
     */
    private String notifyDetail;

    /**
     * 支付回调类型
     */
    private OrderPayNotifyType payNotifyType;

    /**
     * 是否消费
     */
    private Boolean consume;

}
