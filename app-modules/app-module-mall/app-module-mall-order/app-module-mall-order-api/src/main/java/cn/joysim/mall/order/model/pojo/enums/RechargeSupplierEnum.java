package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 充值供应商
 */
public enum RechargeSupplierEnum implements ValueEnum {

    /**
     * 欧飞
     */
    SUPPLIER_OFPAY(0, "欧飞"),
    /**
     * 分销平台
     */
    SUPPLIER_FXPT(1, "分销平台"),
    /**
     * 正联
     */
    SUPPLIER_ZL(2, "正联"),
    /**
     * 方知
     */
    SUPPLIER_FZ(3, "方知"),
    /**
     * 景心自营
     */
    JOYSIM(4, "景心自营"),
    /**
     * 乐语
     */
    SUPPLIER_LY(5, "乐语"),
    /**
     * 万恒
     */
    SUPPLIER_WH(6, "万恒科技"),
    /**
     * 华高
     */
    SUPPLIER_HG(7, "华高"),
    /**
     * 有钱途
     */
    SUPPLIER_YQT(8, "有钱途"),
    /**
     * 韬铭
     */
    SUPPLIER_TM(9, "韬铭"),
    /**
     * E券
     */
    E_QUAN(10, "E券"),

    /**
     * 谦硕
     */
    SUPPLIER_QS(11, "谦硕"),
    /**
     * 卡池
     */
    KA_CHI(12, "卡池"),

    /**
     * 充值中心
     */
    RECHARGE_CENTER(13,"充值中心"),
    ;

    RechargeSupplierEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static RechargeSupplierEnum fromCode(Integer code) {
        for (RechargeSupplierEnum orderTypeEnum : RechargeSupplierEnum.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        throw new OrderException(OrderStatusCode.ORDER_RECHARGE_TYPE_ERROR);
    }

    public static RechargeSupplierEnum fromText(String text) {
        for (RechargeSupplierEnum orderTypeEnum : RechargeSupplierEnum.values()) {
            if(orderTypeEnum.getText().equals(text)) {
                return orderTypeEnum;
            }
        }

        throw new OrderException(OrderStatusCode.ORDER_RECHARGE_TYPE_ERROR);
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
