package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.mall.order.converter.easyExcel.MeiTuanBigDecimalNumberConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/11 14:17
 */
@Data
public class MeiTuanCouponBillDTO {

    @ExcelProperty(order = 1)
    private String type;

    @ExcelProperty("活动ID")
    private String couponBatchId;


    @ExcelProperty("活动名称")
    private String couponBatchName;

    @ExcelProperty("券码")
    private String code;

    @ExcelProperty("兑换码")
    private String redeemCode;

    @ExcelProperty("支付银行")
    private String bankName;

    @ExcelProperty("支付通道")
    private String payChannelName;

    /**
     * 账单类型=【支付】时，该字段指：支付成功时间
     * 账单类型=【退款】时，该字段指：退款成功时间
     */
    @ExcelProperty(value = "交易成功时间",converter = ConverterDateTime.class)
    private Date sucTime;

    /**
     * 账单类型=【退款】时，该字段指原支付单金额
     */
    @ExcelProperty(value = "支付单金额",converter = MeiTuanBigDecimalNumberConverter.class)
    private BigDecimal payMoney;

    /**
     * 账单类型=【支付】时，该字段指：支付优惠金额
     *
     * 账单类型=【退款】时，该字段指：原活动优惠金额
     */
    @ExcelProperty(value = "活动优惠金额",converter = MeiTuanBigDecimalNumberConverter.class)
    private BigDecimal reduceMoney;

    /**
     * 账单类型=【支付】时，该字段指：银行卡支付金额
     *
     * 账单类型=【退款】时，该字段指：银行卡退款金额
     */
    @ExcelProperty(value = "银行卡交易金额",converter = MeiTuanBigDecimalNumberConverter.class)
    private BigDecimal bankMoney;


    @ExcelProperty(value = "优惠实际退款金额",converter = MeiTuanBigDecimalNumberConverter.class)
    private BigDecimal promMoney;

    @ExcelProperty("卡的前六后四位")
    private String first6last4;

    @ExcelProperty("请求银行支付的流水号")
    private String reqBankTradeNo;

    @ExcelProperty("银行返回流水号")
    private String repBankTradeNo;

    @ExcelProperty("银行卡ID")
    private String bankcardId;

}
