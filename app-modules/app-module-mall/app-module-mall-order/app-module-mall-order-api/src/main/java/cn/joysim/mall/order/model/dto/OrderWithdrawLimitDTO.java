package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.WithdrawCashVersion;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: linhs
 * @date: 2020/6/5
 * @desc: 提现操作的限制参数
 */
@Data
public class OrderWithdrawLimitDTO {
    /**
     * 提现开关
     * 0：关闭
     * 1：开启
     */
    private Integer makeCashSwitch;

    /**
     * 当天限制次数
     */
    private Integer num;

    /**
     * 单日金额限制
     */
    private BigDecimal amount;

    /**
     * 单笔金额限制
     */
    private BigDecimal eachAmount;

    /**
     * 开启红包开关
     * 0：关闭
     * 1：开启
     */
    private  Integer redPacketSwitch;

    /**
     * 单个红包最大限制
     */
    private BigDecimal redPacketAmount;

    /**
     * 当天红包限制次数
     */
    private Integer numRed;

    /**
     * 单日红包金额限制
     */
    private BigDecimal redAmount;

    /**
     * 小程序AppId
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 支付签名key
     */
    private String paySignKey;

    /**
     * 证书路径
     */
    private String certPath;

    /**
     * 提现缓冲时间
     */
    private Integer bufferTime;

    /**
     * 活动津贴发放上线
     */
    private BigDecimal allowanceLimit;

    /**
     * 开始时间 09:00:00
     */
    private String startTimeStr;

    /**
     * 结束时间 18:00:00
     */
    private String endTimeStr;

    /**
     * 最大交易金额
     */
    private BigDecimal maxTradeAmount;

    /**
     * 当前提现接口版本
     */
    private WithdrawCashVersion withdrawCashVersion;

    /**
     * 商家转账商户号
     */
    private String sellerMchId;
    /**
     * 商家转账支付签名key
     */
    private String sellerPaySignKey;

    /**
     * 商家转账证书路径
     */
    private String sellerCertPath;

    /**
     * 回调地址
     */
    private String notifyUrl;

    /**
     * 拆分面额单元
     */
    private BigDecimal faceValueUnit;
}
