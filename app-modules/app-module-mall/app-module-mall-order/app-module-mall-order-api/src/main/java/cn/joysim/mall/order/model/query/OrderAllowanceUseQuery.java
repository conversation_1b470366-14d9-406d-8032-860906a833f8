package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.AllowanceType;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.1.1
 * @author: linhs
 * @date: 2020/9/7
 * @desc: 积分使用统计查询类
 */
@Data
public class OrderAllowanceUseQuery implements Serializable {

    /**
     * 积分类型
     *
     */
    private AllowanceType allowanceType;

    /**
     * 查询开始时间
     * "yyyy-MM-dd"
     */
    private String startDate;

    /**
     * 查询结束时间
     * "yyyy-MM-dd"
     */
    private String endDate;

}
