package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/4
 * @desc: 提现订单状态
 */
public enum OrderWithdrawFailReasionEnum implements ValueEnum {

    /**
     * 账户冻结
     */
    ACCOUNT_FROZEN(0,"账户冻结"),
    /**
     * 用户未实名
     */
    REAL_NAME_CHECK_FAIL(1, "用户未实名"),

    /**
     * 用户姓名校验失败
     */
    NAME_NOT_CORRECT(2, "用户姓名校验失败"),

    /**
     * Openid校验失败
     */
    OPENID_INVALID(3, "Openid校验失败"),

    /**
     * 超过用户单笔收款额度
     */
    TRANSFER_QUOTA_EXCEED(4, "超过用户单笔收款额度"),

    /**
     * 超过用户单日收款额度
     */
    DAY_RECEIVED_QUOTA_EXCEED(5,"超过用户单日收款额度"),

    /**
     * 超过用户单月收款额度
     */
    MONTH_RECEIVED_QUOTA_EXCEED(6,"超过用户单月收款额度"),

    /**
     * 超过用户单日收款次数
     */
    DAY_RECEIVED_COUNT_EXCEED(7,"超过用户单日收款次数"),

    /**
     * 产品权限校验失败
     */
    PRODUCT_AUTH_CHECK_FAIL(8,"产品权限校验失败"),

    /**
     * 转账关闭
     */
    OVERDUE_CLOSE(9, "转账关闭"),

    /**
     * 用户身份证校验失败
     */
    ID_CARD_NOT_CORRECT(10, "用户身份证校验失败"),
    /**
     * 用户账户不存在
     */
    ACCOUNT_NOT_EXIST(11, "用户账户不存在"),
    /**
     * 转账存在风险
     */
    TRANSFER_RISK(12, "转账存在风险"),
    /**
     * 用户账户收款受限
     */
    REALNAME_ACCOUNT_RECEIVED_QUOTA_EXCEED(13, "用户账户收款受限"),
    /**
     * 未配置该用户为转账收款人
     */
    RECEIVE_ACCOUNT_NOT_PERMMIT(14, "未配置该用户为转账收款人"),
    /**
     * 用户身份证校验失败
     */
    PAYER_ACCOUNT_ABNORMAL(15, "商户账户付款受限，可前往商户平台-违约记录获取解除功能限制指引"),
    /**
     * 用户账户收款异常，请引导用户完善其在微信支付的身份信息以继续收款
     */
    PAYEE_ACCOUNT_ABNORMAL(16, "用户账户收款异常，请引导用户完善其在微信支付的身份信息以继续收款"),

    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderWithdrawFailReasionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}


