package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.integer.IntegerStringConverter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/7/26 17:28
 */
@Data
public class WeComUserTagExportDTO implements Serializable {


    /**
     * 用户Id
     */
    @ExcelProperty(value="用户Id",converter = LongStringConverter.class)
    private Long userId;

    /**
     * openId
     */
    @ExcelProperty("openId")
    private String openId;

    /**
     * unionId
     */
    @ExcelProperty("unionId")
    private String unionId;


    /**
     * 用户昵称
     */
    @ExcelProperty("用户昵称")
    private String nickname;


    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String mobile;


    /**
     * 群聊名称
     */
    @ExcelProperty("客户联系人")
    private String externalUserId;

}
