package cn.joysim.mall.order.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/2/3
 */
@Data
@NoArgsConstructor
public class SendTicketDTO implements Serializable {

    /**
     * 单号
     */
    private String orderId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * ESM产品Id
     */
    private Integer productId;
    /**
     * 产品面值
     */
    private Double faceValue;
    /****************以下三个参数仅用于车主升级发券*****************/
    /**
     * 升级目标权益Id
     */
    private Long targetProductId;
    /**
     * 升级补偿差价
     */
    private BigDecimal compensationPrice;
    /**
     * 源券码
     */
    private String sourceCode;

    public SendTicketDTO(String orderId, String mobile, Integer quantity) {
        this.orderId = orderId;
        this.mobile = mobile;
        this.quantity = quantity;
    }

    public String getMobiles() {
        if (this.quantity > 1) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < quantity; i++) {
                sb.append(this.mobile).append(",");
            }

            return sb.deleteCharAt(sb.lastIndexOf(",")).toString();
        } else {
            return this.mobile;
        }
    }

}
