package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/2/20
 * @desc: 退款参数
 */
@Data
@Accessors(chain = true)
public class OrderRefundParamsDTO implements Serializable {
    private Long orderId;
    private String remark;
    private String operatorMan;
    private OrderTicketStateEnum afterRefundState;
    /**
     * 是否微信代金券
     */
    private Boolean isWx;

    /**
     * 券记录id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long ticketRecordId;
}
