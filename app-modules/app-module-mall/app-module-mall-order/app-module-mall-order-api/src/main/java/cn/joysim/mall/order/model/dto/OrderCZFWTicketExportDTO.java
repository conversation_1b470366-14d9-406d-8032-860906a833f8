package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/5/21
 * 车主服务订单导出
 */
@Data
public class OrderCZFWTicketExportDTO implements Serializable {

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String id;

    /**
     * 用户ID
     */
    @ExcelProperty("用户ID")
    private String userId;

    /**
     * 卡券类型
     */
    @ExcelProperty("卡券类型")
    private String categoryName;
    /**
     * 卡券名称
     */
    @ExcelProperty("卡券名称")
    private String skuFinalName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String rechargeAccount;

    /**
     * 实付金额
     */
    @ExcelProperty("实付金额")
    private BigDecimal cashTotalAmount;

    /**
     * 下单时间
     */
    @ExcelProperty("下单时间")
    private String orderTime;

    /**
     * 订单状态
     */
    @ExcelProperty("订单状态")
    private String stateStr;

}
