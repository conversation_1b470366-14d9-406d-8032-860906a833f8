package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName tb_mall_order_ticket_receive_expire_notify_log
 */
@TableName(value ="tb_mall_order_ticket_receive_expire_notify_log")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderTicketReceiveExpireNotifyLog implements Serializable {
    @TableId
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long id;
    private Date createTime;

    /**
     * tb_mall_order_ticket_detail.id
     */
    private Long ticketDetailId;

}