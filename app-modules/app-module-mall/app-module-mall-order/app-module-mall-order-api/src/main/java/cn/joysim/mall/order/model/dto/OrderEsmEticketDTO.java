package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.product.model.pojo.enums.SplitJointType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.NumberFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version: 2.4.8
 * @author: linhs
 * @date: 2021/9/10
 * @desc: 卡券列表项
 */
@Data
public class OrderEsmEticketDTO implements Serializable {
    /**
     * 券id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 订单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 电子券id(esm)
     */
    private Integer ticketId;

    /**
     * 卡券订单详情id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long detailId;
    /**
     * 产品id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    /**
     * 券码
     */
    private String code;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 可使用总数
     */
    private int totalTimes;

    /**
     * 已使用次数
     */
    private int usedTimes;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU结算名称
     */
    private String skuName;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 有效期
     */
    private Date expiredTime;

    /**
     * 类型
     */
    private String category;

    /**
     * 金额
     */
    private String price;

    /**
     * 券类型
     * 200代表易加油优惠券
     *  0 滴滴权益
     * 2 易捷
     * 6 电子加油券
     * 7 e券-京东e卡
     * 11 e券-天猫享淘券
     * 13 麦当劳电子券
     * 19 e券-猫眼电影券
     * 22 e券-星巴克
     * 27 支付宝立减金券
     * 100 待激活微信代金券
     * 101 系统优惠券
     * 102 E券油卡
     * 103 银行商城优惠券
     * 104 千人千券
     * 200 易加油
     */
    private Integer refType;


    /**
     * 拼接类型
     *
     */
    private SplitJointType jointType;

    /**
     * 微信优惠券Id
     */
    private String couponId;
    /**
     * 微信优惠券批次Id
     */
    private String couponStockId;

    /**
     * 自定义扩展信息（可随业务需要使用）
     * custom_ext，这里放业务数据
     *
     * sendAmount 加油金赠送面额
     * sendSkuId 加油金赠送产品Id
     * preferential 是否显示优惠标签
     * desc 券说明，若券说明有写则在“我的卡券”下的“券说明”展示填入的字段（客户端最多展示2行），若没填则展示固定话术：指定门店微信支付时直接使用
     * cardHolderId 卡包id,点击跳转到微信卡包需要填写该字段
     * special
     * special.sendSkuId 赠送skuId
     * special.quantity 赠送次数
     * special.sendAmount 赠送面额
     *
     */
    @JsonIgnore
    private String customExt;


    /**
     * customExt.desc
     */
    private String desc;


    /**
     * 优惠门槛
     */
    @NumberFormat(pattern = "#.##")
    private BigDecimal threshold;


    /**
     * 卡包Id
     * customExt.desc
     */
    private String cardHolderId;

    /**
     * 卡券有效开始时间，为expiredTime的补充
     */
    private Date beginTime;

    /**
     * 卡券有效结束时间，为expiredTime的补充
     */
    private Date endTime;

    /**
     * 激活码
     */
    private String activateCode;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 券状态
     */
    private Integer ticketState;

}
