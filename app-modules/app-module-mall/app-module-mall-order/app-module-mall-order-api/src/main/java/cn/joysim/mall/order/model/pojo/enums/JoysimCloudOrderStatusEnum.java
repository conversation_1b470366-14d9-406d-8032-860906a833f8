package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

public enum JoysimCloudOrderStatusEnum implements ValueEnum {

    /**
     * 订单初始态，订单未完成充值，请稍后再查
     */
    SUBMITTED(0, "已提交"),

    /**
     * 订单过程态，订单未完成充值，如果卡这个状态很久，可以联系人工核实
     */
    PUSHING(1, "推送中"),

    /**
     * 订单过程态，订单未完成充值
     */
    RECHARGING(2, "充值中"),

    /**
     * 订单终态，订单已完成充值，且充值成功
     */
    SUCCESS(3, "充值成功"),

    /**
     * 订单终态，订单已完成充值，且充值失败
     */
    FAILURE(4, "充值失败"),

    /**
     * 订单过程态，订单未完成充值，请联系人工处理
     */
    MANUAL_AUDIT(5, "等待人工审核");

    @EnumValue
    private final int code;
    private final String text;

    JoysimCloudOrderStatusEnum(int code, String description) {
        this.code = code;
        this.text = description;
    }

    /**
     * 获取状态码
     */
    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

}