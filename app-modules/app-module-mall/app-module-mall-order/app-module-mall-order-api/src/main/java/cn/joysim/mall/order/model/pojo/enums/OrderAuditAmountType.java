package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/3
 * @desc:
 */
public enum OrderAuditAmountType implements ValueEnum {

    /**
     * 电子券
     */
    TICKET(0, "电子券"),
    /**
     * 财付通
     */
    MCH(1, "财付通"),

    ;

    OrderAuditAmountType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }



    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }


    public static OrderAuditAmountType fromText(String text) {
        for (OrderAuditAmountType aliPayEnum : OrderAuditAmountType.values()) {
            if(aliPayEnum.getText().equals(text)) {
                return aliPayEnum;
            }
        }
        return null;
    }

}


