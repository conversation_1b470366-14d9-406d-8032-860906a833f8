package cn.joysim.mall.order.model.pojo;

import cn.joysim.mall.order.model.pojo.enums.RechargeStateEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/14
 * 充值记录
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_recharge_record")
public class OrderRechargeRecord {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 外部单号
     */
    private String externalOrderId;
    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;
    /**
     * 产品skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 充值编码（话费为面值,流量为规则,娱乐为产品Id）
     */
    private String chargeCode;
    /**
     * 充值金额
     */
    private BigDecimal amount;
    /**
     * 充值状态
     */
    private RechargeStateEnum state;
    /**
     * 充值类型(0：快充，1：慢充，2：娱乐，3：流量)
     */
    private RechargeTypeEnum rechargeType;
    /**
     * 充供应商(0：欧飞，1：分销，2：正联)
     */
    private RechargeSupplierEnum rechargeSupplier;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 优惠总金额
     */
    private BigDecimal  discountTotalAmount;
    /**
     * 充值结果
     */
    private String result;
    /**
     * 充值返回信息
     */
    private String rechargeInfo;
    /**
     * 提交充值时间
     */
    private Date submitTime;
    /**
     * 充值成功时间
     */
    private Date successTime;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 结算名称
     */
    private String skuFinalName;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 面值
     */
    private BigDecimal faceValue;

    /**
     * 销售价
     */
    private BigDecimal sellPrice;

    /**
     * 操作人
     */
    private String operator;
}
