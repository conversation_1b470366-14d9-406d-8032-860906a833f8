package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * Created by zhuzl on 2020/2/13.
 */
public enum ShoppingCartOperateType implements ValueEnum {

    /**
     * 减少
     */
    REDUCE(0, "减少"),
    /**
     * 增加
     */
    INCREASE(1, "增加");

    @EnumValue
    private Integer code;
    private String text;

    ShoppingCartOperateType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
