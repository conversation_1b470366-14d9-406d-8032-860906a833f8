package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@Accessors(chain = true)
public class OrderBankActivitySignUpAwardDTO implements Serializable {




    /**
     * 活动Id
     */
    @NotNull(message = "活动Id不能为空")
    private Long activitySignUpId;


    private String orderIp;

    /**
     * 订单来源
     */
    private OrderSource source;

}
