package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankType;
import cn.joysim.mall.order.model.pojo.enums.OrderAuditAmountType;
import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_audit_data")
public class OrderAuditData extends BaseEntity {


    private Date orderDate;

    /**
     * 类型
     */
    private OrderAuditDataType auditDataType;


    /**
     * 原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 剩余金额
     */
    private BigDecimal surplusAmount;

    /**
     * 面额范围
     */
    private String faceArea;

    /**
     * 所属企业
     */
    private String enterpriseName;


    private OrderAuditAmountType amountType;


    /**
     * 费率
     */
    private BigDecimal serviceRate;

}
