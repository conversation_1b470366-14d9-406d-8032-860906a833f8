package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/16
 * @desc: 提现接口版本
 */
public enum WithdrawCashVersion implements ValueEnum {
    ENTERPRISE_API(0, "企业转账"),
    SELLER_API(1, "商家转账"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    WithdrawCashVersion(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    @JsonCreator
    public static WithdrawCashVersion fromCode(Integer code) {
        for (WithdrawCashVersion stateEnum : WithdrawCashVersion.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }
}
