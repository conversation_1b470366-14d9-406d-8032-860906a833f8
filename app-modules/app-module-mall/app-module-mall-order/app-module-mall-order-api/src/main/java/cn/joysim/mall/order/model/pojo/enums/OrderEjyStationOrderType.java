package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/03/15
 * 易加油油站排序方式
 */
public enum OrderEjyStationOrderType implements ValueEnum {

    //油站的排序方式
    DISTANCE(0,"距离优先"),
    PRICE(1,"价格优先"),
    STARNUM(2,"评分优先"),;
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEjyStationOrderType fromCode(Integer code) {
        for (OrderEjyStationOrderType stateEnum : OrderEjyStationOrderType.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEjyStationOrderType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
