package cn.joysim.mall.order.model.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 券补发记录
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class OrderTicketReissueRecordQuery implements Serializable {

    /**
     * 发券记录id
     */
    private Long ticketSendRecordId;

}
