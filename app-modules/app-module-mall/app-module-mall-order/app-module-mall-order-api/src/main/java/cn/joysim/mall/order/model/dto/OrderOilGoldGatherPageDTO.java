package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/18
 * @desc: 加油金汇总
 */
@Data
public class OrderOilGoldGatherPageDTO implements Serializable {

    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 加油金余额
     */
    private String oilGoldSurplus;
    /**
     * 客户收益
     */
    private BigDecimal userIncome;
    /**
     * 班马收益
     */
    private BigDecimal bmIncome;
}
