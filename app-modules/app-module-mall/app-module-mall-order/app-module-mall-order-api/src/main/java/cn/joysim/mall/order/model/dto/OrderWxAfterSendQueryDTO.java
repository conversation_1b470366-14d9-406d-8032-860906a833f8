package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/8
 * @desc:
 */
@Data
public class OrderWxAfterSendQueryDTO implements Serializable {
    /**
     * 券记录id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long ticketRecordId;

}
