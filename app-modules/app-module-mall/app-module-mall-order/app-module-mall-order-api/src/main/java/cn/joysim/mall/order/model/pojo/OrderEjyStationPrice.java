package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/15
 * 油站价格
 */
@Data
@TableName("tb_mall_order_ejy_station_price")
public class OrderEjyStationPrice extends BaseEntity {

    /**
     * 油站id
     */
    String stationId;

    /**
     * 油号 id
     */
    String oilId;

    /**
     * 油号
     */
    String oilCode;

    /**
     * 油类型，1.汽油 2.柴油
     */
    int oilType;

    /**
     * 油站挂牌价
     */
    String stationPrice;

    /**
     * 优惠后单价
     */
    String discountPrice;

    /**
     * 国家价
     */
    String countryPrice;

    /**
     * 油枪号列表
     */
    String oilgunCodes;

}
