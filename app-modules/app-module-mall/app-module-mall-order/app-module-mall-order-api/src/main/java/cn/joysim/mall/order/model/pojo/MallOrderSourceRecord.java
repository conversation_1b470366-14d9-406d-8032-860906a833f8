package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订单来源信息
 * @TableName tb_mall_order_source_record
 */
@TableName(value ="tb_mall_order_source_record")
@Data
public class MallOrderSourceRecord extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 兑换专区ID
     */
    private Long exchangeId;

    /**
     * 订单ID
     */
    private Long orderId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}