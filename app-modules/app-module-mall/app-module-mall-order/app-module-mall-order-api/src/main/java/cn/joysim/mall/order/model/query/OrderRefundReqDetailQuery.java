package cn.joysim.mall.order.model.query;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/15
 * @desc: 退款申请
 */
@Data
public class OrderRefundReqDetailQuery{

    /**
     * 订单单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 是否退款
     */
    private Boolean refund;

}
