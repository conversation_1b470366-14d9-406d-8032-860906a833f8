package cn.joysim.mall.order.model.dto.bm.ty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/3
 * @desc: 团油小程序订单结算页参数
 */
@Data
public class OrderBmMiniTySettleParamDTO implements Serializable {

    /**
     * 油站ID
     */
    private String gasId;

    /**
     * 加油⾦额
     */
    private BigDecimal fuelAmount;

    /**
     * 油号
     */
    private Integer oilNo;

    /**
     * 油枪号
     */
    private Integer gunNo;

    /**
     * 使用津贴
     */
    private Boolean useAllowance;

    /**
     * 用户Id
     */
    private Long userId;
}
