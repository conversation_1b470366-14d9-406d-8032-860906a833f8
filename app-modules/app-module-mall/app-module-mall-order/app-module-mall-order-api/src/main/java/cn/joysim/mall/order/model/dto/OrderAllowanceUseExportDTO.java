package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.1.1
 * @author: linhs
 * @date: 2020/10/19
 * @desc: 积分使用统计导出
 */
@Data
public class OrderAllowanceUseExportDTO implements Serializable {

    /**
     * 时间粒度
     */
    @ExcelField(title = "时间", sort = 1)
    private String time;

    /**
     * 消耗积分值
     */
    @ExcelField(title = "消耗积分值", sort = 2)
    private String useAllowance;

    /**
     * 积分类型
     */
    @ExcelField(title = "积分类型", sort = 3)
    private String allowanceType;

    /**
     * 订单类型
     */
    @ExcelField(title = "订单类型", sort = 4)
    private String orderType;

    /**
     * 手机号
     */
    @ExcelField(title = "手机号", sort = 5)
    private String mobile;

    /**
     * 变动后余额
     */
    @ExcelField(title = "变动后余额", sort = 6)
    private String lastAllowance;

    /**
     * 关联订单号
     */
    @ExcelField(title = "关联订单号", sort = 7)
    private String orderId;

    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID", sort = 8)
    private String userId;
}
