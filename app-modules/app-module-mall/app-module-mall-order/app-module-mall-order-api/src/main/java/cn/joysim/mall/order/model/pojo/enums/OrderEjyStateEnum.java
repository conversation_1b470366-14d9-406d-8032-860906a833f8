package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 易加油订单状态
 */
public enum OrderEjyStateEnum implements ValueEnum {

    /**
     * 已取消
     */
    CANCEL(-1, "已取消"),
    /**
     * 未支付
     */
    UN_PAY(0, "未支付"),
    /**
     * 已支付
     */
    PAYED(1, "已支付"),
    /**
     * 已提交
     */
    SUBMIT(2, "已提交"),

    /**
     * 充值失败
     */
    FAILED(3, "充值失败"),

    /**
     * 充值成功
     */
    SUCCESS(4, "充值成功"),

    /**
     * 退款中
     */
    REFUNDING(5, "退款中"),
    /**
     * 退款成功
     */
    REFUNDED(6, "退款成功"),
    /**
     * 退款失败
     */
    REFUNDED_FAIL(7, "退款失败"),
    /**
     * 通知成功
     */
    PAYMENTS_SUCCESS(8, "通知成功"),
    /**
     * 通知失败
     */
    PAYMENTS_FAIL(9, "通知失败"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEjyStateEnum fromCode(Integer code) {
        for (OrderEjyStateEnum stateEnum : OrderEjyStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEjyStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
