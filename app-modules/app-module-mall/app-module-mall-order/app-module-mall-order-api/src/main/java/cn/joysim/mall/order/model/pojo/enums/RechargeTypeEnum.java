package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.AppConst;
import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.common.model.pojo.enums.ProductCategorySettleType;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 充值类型
 */
public enum RechargeTypeEnum implements ValueEnum {

    /**
     * 话费快充
     */
    FAST_RECHARGE(0, "话费快充", AppConst.R_FALE_FALST, ProductCategorySettleType.FAST_RECHARGE),
    /**
     * 话费慢充
     */
    SLOW_RECHARGE(1, "话费慢充", AppConst.R_FALE_SLOW, ProductCategorySettleType.SLOW_RECHARGE),
    /**
     * 娱乐充值
     */
    MEDIA_RECHARGE(2, "娱乐充值", AppConst.R_MEDIA, ProductCategorySettleType.MEDIA_RECHARGE),
    /**
     * 流量充值
     */
    MOBILE_TRAFFIC_RECHARGE(3, "流量充值",AppConst.R_FLOW, ProductCategorySettleType.MOBILE_TRAFFIC_RECHARGE),
    /**
     * 班点充值
     */
     BAN_DIAN_RECHARGE(4, "班点充值",AppConst.R_BAN_DIAN, ProductCategorySettleType.BAN_DIAN_RECHARGE),
    /**
     * 生活充值
     */
    LIFE_RECHARGE(5, "生活充值",AppConst.R_LIFE, ProductCategorySettleType.LIFE_RECHARGE),
    /**
     * 加油金
     */
    OIL_GOLD(6, "加油金",AppConst.R_OIL_GOLD, ProductCategorySettleType.OIL_GOLD),
    /**
     * 中石化加油卡
     */
    R_SINOPEC_OIL_CARD(7, "中石化加油卡", AppConst.R_SINOPEC_OIL_CARD, ProductCategorySettleType.R_SINOPEC_OIL_CARD),
    /**
     * 中石油加油卡
     */
    R_CNPC_OIL_CARD(8, "中石油加油卡", AppConst.R_CNPC_OIL_CARD, ProductCategorySettleType.R_CNPC_OIL_CARD),

    /**
     * 津贴充值
     */
    ALLOWANCE_RECHARGE(9, "津贴充值", AppConst.R_ALLOWANCE, ProductCategorySettleType.ALLOWANCE_RECHARGE),

    ;

    RechargeTypeEnum(Integer code, String text, String categoryCode, ProductCategorySettleType productCategorySettleType) {
        this.code = code;
        this.text = text;
        this.categoryCode = categoryCode;
        this.productCategorySettleType = productCategorySettleType;

    }

    public static RechargeTypeEnum fromCode(Integer code) {
        for (RechargeTypeEnum orderTypeEnum : RechargeTypeEnum.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    public static RechargeTypeEnum fromCategoryCode(String categoryCode) {
        for (RechargeTypeEnum orderTypeEnum : RechargeTypeEnum.values()) {
            if(orderTypeEnum.getCategoryCode().equals(categoryCode)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;
    private String categoryCode;

    private ProductCategorySettleType productCategorySettleType;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public ProductCategorySettleType getProductCategoryType() {
        return productCategorySettleType;
    }
}
