package cn.joysim.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/21 15:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCountEJYServiceFeeDTO {


    private BigDecimal targetServiceFee;

    private BigDecimal targetAmount;
}
