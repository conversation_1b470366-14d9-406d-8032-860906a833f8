package cn.joysim.mall.order.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/08/25
 */
@Data
@Accessors(chain = true)
public class OrderOilGoldLimitQuery implements Serializable {

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 充值产品
     */
    private Long skuId;
    /**
     * 用户Id
     */
    private Long userId;
}
