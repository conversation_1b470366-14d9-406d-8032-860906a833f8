package cn.joysim.mall.order.model.pojo;


import cn.joysim.common.model.pojo.enums.EncryptTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/17
 * @desc: 用户下单公私钥
 */
@Data
@TableName("tb_mall_order_encrypt")
public class OrderEncrypt{

    private Integer id;
    /**
     * 加密类型
     */
    private EncryptTypeEnum encryptType;
    /**
     * 公钥
     */
    public String publicKey;
    /**
     * 私钥
     */
    public String privateKey;

    private Date createTime;

}
