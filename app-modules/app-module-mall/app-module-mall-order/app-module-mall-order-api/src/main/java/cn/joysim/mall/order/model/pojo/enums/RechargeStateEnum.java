package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 充值记录状态
 */
public enum RechargeStateEnum implements ValueEnum {

    /**
     * 未提交
     */
    UN_COMMIT(0, "未提交"),
    /**
     * 已提交
     */
    UN_SUBMIT(1, "已提交"),
    /**
     * 充值成功
     */
    SUCCESS(2, "充值成功"),
    /**
     * 充值失败
     */
    FAILURE(3, "充值失败"),
    /**
     * 已退款
     */
    REFUNDED(4, "已退款"),

    /**
     * 部分成功，部分失败
     */
    PART_SUCCESS_PART_FAILURE(5, "部分成功，部分失败"),;
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static RechargeStateEnum fromCode(Integer code) {
        for (RechargeStateEnum stateEnum : RechargeStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    RechargeStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
