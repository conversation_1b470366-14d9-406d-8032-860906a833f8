package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderDiscountDetailDTO implements Serializable {

    /**
     * 折扣类型
     */
    private Integer discountType;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 优惠规则Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long ruleId;

}
