package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderActiveState;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderMainDTO implements Serializable {

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 现金总金额
     */
    private BigDecimal cashTotalAmount;
    /**
     * 津贴支付总金额
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 获得总积分
     */
    private Integer obtainTotalIntegral;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 总成本
     */
    private BigDecimal totalCost;
    /**
     * 邮费
     */
    private Integer postage;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 客服备注
     */
    private String serviceRemark;
    /**
     * 收货号码
     */
    private String receiverMobile;
    /**
     * 团购Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long groupId;
    /**
     * 激活时间
     */
    private Date activeTime;
    /**
     * 激活状态
     */
    private OrderActiveState activeState;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 订单来源(0:小程序,1:公众号)
     */
    private OrderSource source;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;


    /**
     * 兑换专区
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 签收时间
     */
    private Date signTime;


}
