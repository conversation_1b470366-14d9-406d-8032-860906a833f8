package cn.joysim.mall.order.model.dto;

import cn.hutool.core.map.MapUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/14
 * 易加油优惠券
 */
@Data
public class OrderEjyCouponDTO implements Serializable {

    /**
     * 优惠券 id,优惠券唯一表示
     */
    String userCouponId;

    /**
     * 优惠券名字
     */
    String name;

    /**
     * 名字扩展
     */
    String nameExt;

    /**
     * 优惠券有效期
     */
    String limitTime;

    /**
     * 有效期扩展说明
     */
    String limitTimeExplain;

    /**
     * 优惠券类型 1 直减券（现金券） 2 折扣券， 6 直降券,直降是每升可直接直降的金额，但是直减券是在总金额的基础上在直减
     */
    int merchandiseType;


    /**
     * 优惠券值
     */
    String value;

    /**
     * 优惠券限制金额解释,满多少可用
     */
    String valueExplain;

    /**
     * 1 为可用，2 为不可用
     */
    int enabled;

    /**
     * 不可用原因 如可用 本字段为 null
     */
    String unavailableCause;

    /**
     * 更多规则
     */
    String moreRules;

    /**
     * 可用油站数,-1 为全部可用
     */
    int stationNum;

    /**
     * 优惠券类型 1 为油品 2 为商品 3 为洗车
     */
    int type;

    /**
     * 限制油站集合
     */
    List<OrderEjyCouponLimitStationDTO> limitStations;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
