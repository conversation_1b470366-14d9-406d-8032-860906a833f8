package cn.joysim.mall.order.model.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 
 * @TableName tb_marketing_boc_e_cny_send_log
 */
@TableName(value ="tb_mall_update_order_id_record")
@Data
public class UpdateOrderIdRecord implements Serializable {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 主订单Id
     */
    private Long orderMainId;

    /**
     * 卡券订单ID
     */
    private Long preTicketOrderDetailId;

    /**
     * 新订单号
     */
    private Long newTicketOrderDetailId;

    /**
     * 旧的卡券订单拆分发券记录ID
     */
    private Long preOrderTicketRecord;

    /**
     * 新的卡券订单拆分发券记录ID
     */
    private Long newOrderTicketRecord;

    /**
     * 查询上一单的发送请求
     */
    private String requestMessage;

    /**
     * 查询上一单发送结果
     */
    private String responseMessage;

}