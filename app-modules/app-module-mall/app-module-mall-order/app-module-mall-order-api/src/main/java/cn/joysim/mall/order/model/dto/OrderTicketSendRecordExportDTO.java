package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.*;
import cn.joysim.mall.marketing.model.pojo.enums.BankActivityMode;
import cn.joysim.mall.order.model.pojo.enums.*;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发券记录导出
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
public class OrderTicketSendRecordExportDTO implements Serializable {

    @ExcelProperty(value = "发券记录ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long ticketRecordId;

    @ExcelProperty(value = "关联活动ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long activityId;

    @ExcelProperty(value = "关联活动名称")
    @ColumnWidth(15)
    private String activityName;

    @DeserializeClassType(BankActivityMode.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "活动模式", converter = EnumConverter.class)
    @ColumnWidth(10)
    private BankActivityMode activityMode;

    @ExcelProperty(value = "订单ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long orderId;

    @DeserializeClassType(OrderTicketStateEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "订单状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private OrderTicketStateEnum orderStatus;

    @ExcelProperty(value = "下单时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date orderTime;

    @ExcelProperty(value = "下单手机号")
    @ColumnWidth(15)
    private String activityRecordMobile;

    @ExcelProperty(value = "充值账号")
    @ColumnWidth(10)
    private String rechargeAccount;

    @ExcelProperty(value = "充值openId")
    @ColumnWidth(10)
    private String openId;

    @ExcelProperty(value = "券产品SKUID",converter = LongStringConverter.class)
    @ColumnWidth(10)
    private Long skuId;

    @ExcelProperty(value = "券产品名称")
    @ColumnWidth(15)
    private String skuName;

    /**
     * 组合礼包id
     */
    @ExcelProperty(value = "关联组合礼包ID",converter = LongStringConverter.class)
    @ColumnWidth(10)
    private Long composeGiftId;

    /**
     * 组合礼包名称
     */
    @ExcelProperty(value = "关联组合礼包名称")
    @ColumnWidth(10)
    private String composeGiftName;

    @ExcelProperty(value = "券产品条码")
    @ColumnWidth(15)
    private String chargeCode;

    @DeserializeClassType(TicketTypeEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "卡券类型", converter = EnumConverter.class)
    @ColumnWidth(10)
    private TicketTypeEnum ticketType;

    @ExcelProperty(value = "面额")
    @ColumnWidth(10)
    private BigDecimal faceValue;

    @DeserializeClassType(TicketStateEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "券记录状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private TicketStateEnum ticketRecordState;

    @ExcelProperty(value = "预发券时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date preSendTicketTime;

    @ExcelProperty(value = "发券时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date sendTicketTime;

    @ExcelProperty(value = "景心券码")
    @ColumnWidth(10)
    private String ticketCode;

    @DeserializeClassType(WechatCouponState.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "券状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private WechatCouponState ticketStatus;

    @ExcelProperty(value = "消费时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date consumeTime;

    @ExcelProperty(value = "到期时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date expiredTime;


    @ExcelProperty(value = "领取过期时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date receiveExpireDate;

    @ExcelProperty(value = "发券结果")
    @ColumnWidth(15)
    private String sendTicketResult;

    @ExcelProperty(value = "网点二维码ID")
    @ColumnWidth(15)
    private String qrCodeId;

    @ExcelProperty(value = "发券人手机号")
    @ColumnWidth(15)
    private String sendManMobile;

    @ExcelProperty(value = "第三方活动id")
    @ColumnWidth(15)
    private String thirdBatchId;


    @ExcelProperty(value = "第三方券码")
    @ColumnWidth(15)
    private String thirdCouponId;

    /**
     * 平台
     */
    @ExcelIgnore
    private Integer platform;

    /**
     * 充供应商
     */
    @ExcelIgnore
    private TicketSupplierEnum ticketSupplier;
}
