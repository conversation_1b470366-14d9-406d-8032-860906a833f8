package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderAuditDataRecordPageDTO  {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 手机号
     */
    private String rechargeMobile;

    /**
     * 订单状态
     */
    private String orderState;

    /**
     * 订单类型
     */
    private OrderAuditDataType auditDataType;

    /**
     * 订单类型文本
     */
    private String auditDataTypeStr;


    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 电子券支付
     */
    private BigDecimal couponPayAmount;


    /**
     * 财付通支付
     */
    private BigDecimal mchPayAmount;


    /**
     * 所属企业
     */
    private String enterpriseName;

    /**
     * 订单时间
     */
    private Date orderTime;


    /**
     * 活动金额
     */
    private BigDecimal activityAmount;








    public String getOrderTypeStr(){
        return auditDataType.getText();
    }

}
