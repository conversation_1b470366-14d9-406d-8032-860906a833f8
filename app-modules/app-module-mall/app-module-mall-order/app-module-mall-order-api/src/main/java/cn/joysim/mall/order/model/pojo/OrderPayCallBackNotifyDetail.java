package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderPayNotifyType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/4/15
 * @desc: 支付回调明细
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_pay_call_back_notify_detail")
public class OrderPayCallBackNotifyDetail extends BaseEntity {

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 回调内容
     */
    private String notifyDetail;

    /**
     * 支付回调类型
     */
    private OrderPayNotifyType payNotifyType;

    /**
     * 是否消费
     */
    private Boolean consume;

}
