package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/2/2
 * @desc: 分享活动发放奖励参数
 */
@Data
@Accessors(chain = true)
public class OrderBankShareActivityGrantRewardParamsDTO implements Serializable {

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 手机号
     */
    private String userMobile;

    /**
     * 被邀请的用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeUserId;

    /**
     * 邀请记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteRecordId;

    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 外部用户号
     */
    private String userCustomerId;

    /**
     * 被邀请人外部用户号
     */
    private String inviteeCustomerId;
}
