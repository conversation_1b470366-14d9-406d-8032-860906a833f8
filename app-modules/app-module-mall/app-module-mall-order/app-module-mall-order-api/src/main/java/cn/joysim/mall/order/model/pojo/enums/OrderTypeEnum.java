package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Builder;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 订单类型
 */
public enum OrderTypeEnum implements ValueEnum {

    /**
     * 实物订单
     */
    ENTITY_TYPE(0, "实物订单"),
    /**
     * 卡券订单
     */
    TICKET_TYPE(1, "卡券订单"),
    /**
     * 充值订单
     */
    RECHARGE_TYPE(2, "充值订单"),
    /**
     * 易加油订单
     */
    EJY_TYPE(3, "易加油订单"),
    /**
     * 团油订单
     */
    TY_TYPE(4, "团油订单"),

    ;

    OrderTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderTypeEnum fromCode(Integer code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    @Data
    @Builder
    public static class Type2OrderState{
        private Integer orderType;
        private List<Integer> states;
    }

    /**
     * 订单类型与对应类型订单失败状况的映射
     */
    public static final List<Type2OrderState> failType;

    static {
        failType = new LinkedList<>();
        failType.add(Type2OrderState.builder().orderType(OrderTypeEnum.ENTITY_TYPE.getCode()).states(
                Arrays.asList(OrderEntityStateEnum.CANCEL.getCode(),
                        OrderEntityStateEnum.UN_PAY.getCode(),
                        OrderEntityStateEnum.REFUNDED.getCode(),
                        OrderEntityStateEnum.REFUNDING.getCode()))
                .build());
        failType.add(Type2OrderState.builder().orderType(OrderTypeEnum.TICKET_TYPE.getCode()).states(
                Arrays.asList(OrderTicketStateEnum.CANCEL.getCode(),
                        OrderTicketStateEnum.UN_PAY.getCode(),
                        OrderTicketStateEnum.REFUNDED.getCode(),
                        OrderTicketStateEnum.REFUNDING.getCode()))
                .build());
        failType.add(Type2OrderState.builder().orderType(OrderTypeEnum.RECHARGE_TYPE.getCode()).states(
                Arrays.asList(OrderRechargeStateEnum.CANCEL.getCode(),
                        OrderRechargeStateEnum.UN_PAY.getCode(),
                        OrderRechargeStateEnum.REFUNDED.getCode())
        ).build());
    }

}
