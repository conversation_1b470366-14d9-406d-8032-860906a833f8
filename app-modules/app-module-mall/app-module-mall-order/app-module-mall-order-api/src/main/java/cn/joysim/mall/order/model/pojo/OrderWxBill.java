package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @version: 2.4.17
 * @author: linhs
 * @date: 2022/1/14
 * @desc: 微信对账单
 */
@Data
@EqualsAndHashCode
@TableName("tb_mall_order_wx_bill")
public class OrderWxBill extends BaseEntity {


    /**
     * 交易时间
     */
    private String tradeTime;
    /**
     * 公众账号ID
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 特约商户号
     */
    private String specialMchId;
    /**
     * 设备号
     */
    private String deviceInfo;
    /**
     * 微信订单号
     */
    private String transactionId;
    /**
     * 商户订单号
     */
    private String outTradeNo;
    /**
     * 用户标识
     */
    private String openId;
    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 交易状态
     */
    private String tradeState;
    /**
     * 付款银行
     */
    private String bankType;
    /**
     * 货币种类
     */
    private String feeType;
    /**
     * 应结订单金额
     */
    private String settlementTotalFee;
    /**
     * 代金券金额
     */
    private String couponFee;
    /**
     * 微信退款单号
     */
    private String refundId;
    /**
     * 商户退款单号
     */
    private String outRefundNo;
    /**
     * 退款金额
     */
    private String settlementRefundFee;
    /**
     * 充值券退款金额
     */
    private String rechargeRefundFee;
    /**
     * 退款类型
     */
    private String refundType;
    /**
     * 退款状态
     */
    private String refundStatus;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商户数据包
     */
    private String attach;
    /**
     * 手续费
     */
    private String poundage;
    /**
     * 费率
     */
    private String rate;
    /**
     * 订单金额
     */
    private String totalFee;
    /**
     * 申请退款金额
     */
    private String refundFee;
    /**
     * 费率备注
     */
    private String rateRemark;
}
