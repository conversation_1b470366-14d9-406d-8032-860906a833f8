package cn.joysim.mall.order.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油卡充值下单结果
 * <AUTHOR>
 * @date 2020/10/19
 */
@Data
public class OilOrderResultVO implements Serializable {

    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 班点支付总金额
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 订阅消息Id
     */
    private List<String> templateId;
    /**
     * 充值金额
     */
    private BigDecimal rechargeTotalAmount;
    /**
     * 现金支付总金额
     */
    private BigDecimal cashTotalAmount;

}
