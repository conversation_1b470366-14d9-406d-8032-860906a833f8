package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.converter.mybatis.OrderTypeGroupHandler;
import cn.joysim.mall.order.converter.mybatis.WeComCorpTagConditionHandler;
import cn.joysim.mall.order.model.dto.OrderTypeGroupDP;
import cn.joysim.mall.order.model.dto.WeComCorpTagConditionDP;
import cn.joysim.mall.user.converter.mybatis.ListBankActivityAuditorPermissionsHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Date;
import java.util.List;

/**
 * 企微标签
 * <AUTHOR>
 * @date 2022/7/25 9:35
 */
@EqualsAndHashCode
@Data
@TableName(value = "tb_mall_we_com_corp_tag",autoResultMap = true)
public class WeComCorpTag extends BaseEntity {


    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;


    /**
     * 标签组Id
     */
    private String groupId;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 订单类型
     */
    @TableField(typeHandler = OrderTypeGroupHandler.class)
    private OrderTypeGroupDP orderTypes;

    /**
     * 条件
     */
    @TableField(typeHandler = WeComCorpTagConditionHandler.class)
    private List<WeComCorpTagConditionDP> conditions;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;


}
