package cn.joysim.mall.order.model.dto.bm.ty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/3
 * @desc:  团油退款通知参数
 */
@Data
public class OrderTyRefundOrderParamsDTO implements Serializable {

    /**
     * 能链订单号
     */
    private String orderNo;

    /**
     * 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 订单所有状态 0-待⽀付 1-已⽀付 2-退款中 4-订单取消
     * 5-已退款 7-退款失败
     * * 当前状态 0 待⽀付
     *
     */
    private Integer orderStatus;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 请求幂等流⽔号
     */
    private String uuid;

    /**
     * 业务接⼝类型（1=订单推送，2=⽀付推送，3=退款回
     * 告，4=标准收银台）⽤于同⼀url推送不同业务场景
     */
    private String bizType;

}
