package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @version linhs
 * @author: linhs
 * @date: 2020/6/4
 * @desc: 提现订单
 */
@Data
@EqualsAndHashCode
@TableName("tb_mall_order_withdraw_cash")
public class OrderWithdrawCash extends BaseEntity {

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 津贴记录Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long allowanceRecordId;

    /**
     * 奖品id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long boxDetailId;
    /**
     * 订单类型
     * 0：红包
     * 1：津贴
     */
    private OrderCashType cashType;

    /**
     * 订单总金额
     */
    private BigDecimal cashAmount;

    /**
     * 实际到账金额
     */
    private BigDecimal actArrivalAmount;

    /**
     * 订单提现状态
     */
    private OrderWithdrawCashStatus status;

    /**
     * 下单手机号
     */
    private String mobile;

    /**
     * 微信流水号（微信付款单号）
     */
    private String wxDetailId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展信息
     */
    private String ext;

    /**
     * 微信支付成功时间.
     */
    private String paymentTime;

    /**
     * 重新请求接口次数
     */
    //private Integer num;

    /**
     * 商户订单号
     */
    private String partnerTradeNo;

    /**
     * 用户端请求ip
     */
    private String realIp;

    /**
     * 用户openid
     */
    private String  miniOpenId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * appid
     */
    private String appId;

    /**
     * 订单来源(0:小程序,1:公众号)
     */
    private OrderSource source;

    /**
     * 提现接口类型
     * 0：企业转账
     * 1：商家转账
     */
    private WithdrawCashVersion withdrawCashVersion;

    /**
     * 批次状态
     */
    private OrderWithdrawCashBatchStatus batchStatus;


    /**
     * 批次id
     */
    private String batchId;

    /**
     * 提交时间
     */
    private Date submitTime;

}
