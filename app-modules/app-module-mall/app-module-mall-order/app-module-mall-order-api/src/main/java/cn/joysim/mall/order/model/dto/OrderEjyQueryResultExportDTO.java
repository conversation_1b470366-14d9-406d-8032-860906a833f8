package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/6/28
 * @desc: 导出
 */
@Data
public class OrderEjyQueryResultExportDTO implements Serializable {
    @ExcelField(title = "易加油内部订单流水号", sort = 1)
    private String orderId;

    @ExcelField(title = "手机号码", sort = 2)
    private String phone;

    @ExcelField(title = "支付时间", sort = 11)
    private String payOrderTime;

    @ExcelField(title = "省份名称", sort = 3)
    private String province;

    @ExcelField(title = "城市名字", sort = 4)
    private String city;

    @ExcelField(title = "油站名称", sort = 5)
    private String stationName;

    @ExcelField(title = "油号名称", sort = 6)
    private String oilCode;


    @ExcelField(title = "机显金额（元）", sort = 7)
    private String originalCost;


    @ExcelField(title = "优惠券金额（元）", sort = 8)
    private String couponMoney;

    @ExcelField(title = "用户实际支付金额（元）", sort = 9)
    private String orderSum;

    @ExcelField(title = "加油升数（升）", sort = 10)
    private String oilMass;


    @ExcelField(title = "退款时间", sort = 12)
    private String refundTime;

    @ExcelField(title = "状态", sort = 13)
    private String statusStr;

    @ExcelField(title = "支付方式", sort = 14)
    private String payTypeStr;



    private Integer status;

    private String payType;

    public String getStatusStr() {
        switch (status){
            case 0:
                return "待支付";
            case 1:
                return "支付";
            case 2:
                return "退款";
            default:
                return "";
        }
    }

    public String getPayTypeStr() {
        switch (payType){
            case "1":
                return "微信";
            case "2":
                return "支付宝";
            case "52":
                return "第三方支付";
            default:
                return "";
        }
    }
}
