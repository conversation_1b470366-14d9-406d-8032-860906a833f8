package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/03/15
 * 易加油油站油号
 */
public enum OrderEjyOilCodeEnum implements ValueEnum {

    /**
     * 0#
     */
    OIL_0(0, "0#"),
    /**
     * 92#
     */
    OIL_92(1, "92#"),
    /**
     * 95#
     */
    OIL_95(2, "95#"),
    /**
     * 98#
     */
    OIL_98(3, "98#"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEjyOilCodeEnum fromCode(Integer code) {
        for (OrderEjyOilCodeEnum stateEnum : OrderEjyOilCodeEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEjyOilCodeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static OrderEjyOilCodeEnum getByCode(Integer code){
        switch (code){
            case 0:
                return OIL_0;
            case 92:
                return OIL_92;
            case 95:
                return OIL_95;
            case 98:
                return OIL_98;
        }
        return null;
    }
}
