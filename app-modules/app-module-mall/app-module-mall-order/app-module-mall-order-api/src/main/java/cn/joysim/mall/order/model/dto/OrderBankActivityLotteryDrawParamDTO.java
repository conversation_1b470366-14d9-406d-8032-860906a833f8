package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: Administrator
 * @date: 2022/4/25
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderBankActivityLotteryDrawParamDTO implements Serializable {
    private Long userId;
    private Long activityId;
    private Long paymentOrderId;
    /**
     * 充值账号
     */
    private String rechargeMobile;

    /**
     * 银行活动二维码id
     */
    private Long marketingBankActivityQrCodeId;

    /**
     * 银行活动次数来源
     */
    private MarketingBankActivityNumSourceEnum activityNumSource;
}
