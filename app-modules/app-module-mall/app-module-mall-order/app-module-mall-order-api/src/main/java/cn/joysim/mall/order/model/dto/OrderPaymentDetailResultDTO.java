package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付明细
 *
 * <AUTHOR>
 * @date 2019/10/31
 */
@Data
public class OrderPaymentDetailResultDTO implements Serializable {

    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 优惠
     */
    private BigDecimal postage;
}
