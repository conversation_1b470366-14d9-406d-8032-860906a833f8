package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description Created with IDEA
 * @create: 2022-08-17 15:04
 * @since JDK 1.8
 */
public enum MallOrderUniversalReportBillTypeEnum implements ValueEnum {

    /**
     * 选择
     */
    UNKNOWN(0,"未知"),
    /**
     * 新增
     */
    PAY(1,"支付"),
    /**
     * 新增
     */
    REFUND(2,"退款"),

    /**
     * 美团现金券支付
     */
    MEITUAN_CASH_COUPON_PAY(3, "现金券支付"),

    /**
     * 美团现金券退款
     */
    MEITUAN_CASH_COUPON_REFUND(4, "现金券退款"),
    ;

    MallOrderUniversalReportBillTypeEnum(Integer code, String text){
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;

    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static MallOrderUniversalReportBillTypeEnum fromText(String text) {
        return Arrays.stream(values()).filter(e -> e.getText().equals(text)).findFirst().orElse(UNKNOWN);
    }

}
