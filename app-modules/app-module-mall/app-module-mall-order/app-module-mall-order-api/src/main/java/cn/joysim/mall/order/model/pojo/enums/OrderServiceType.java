package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;

/**
 * 订单服务枚举
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public enum OrderServiceType {

    /**
     * 实物订单服务
     */
    ENTITY_ORDER_SERVICE(OrderTypeEnum.ENTITY_TYPE, Constants.ENTITY),
    /**
     * 卡券订单服务
     */
    TICKET_ORDER_SERVICE(OrderTypeEnum.TICKET_TYPE, Constants.TICKET),
    /**
     * 充值订单服务
     */
    RECHARGE_ORDER_SERVICE(OrderTypeEnum.RECHARGE_TYPE, Constants.RECHARGE),
    /**
     * 易加油订单服务
     */
    EJY_ORDER_SERVICE(OrderTypeEnum.EJY_TYPE, Constants.EJY),

    /**
     * 团油订单服务
     */
    TY_ORDER_SERVICE(OrderTypeEnum.TY_TYPE, Constants.TY),

    ;


    private OrderTypeEnum orderType;
    private String text;

    OrderServiceType(OrderTypeEnum orderType, String text) {
        this.orderType = orderType;
        this.text = text;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public String getText() {
        return text;
    }

    public static OrderServiceType fromOrderType(OrderTypeEnum orderType) {
        for (OrderServiceType value : OrderServiceType.values()) {
            if (value.getOrderType().equals(orderType)) {
                return value;
            }
        }

        throw new OrderException(OrderStatusCode.ORDER_TYPE_ERROR);
    }

    public static class Constants {
        public static final String ENTITY = "orderEntityService";
        public static final String TICKET = "orderTicketService";
        public static final String RECHARGE = "orderRechargeService";
        public static final String EJY = "orderEjyService";
        public static final String FREE_ORDER = "orderFreeService";
        public static final String TY = "orderTyService";

        public static final String INSURANCE = "InsuranceService";
    }
}
