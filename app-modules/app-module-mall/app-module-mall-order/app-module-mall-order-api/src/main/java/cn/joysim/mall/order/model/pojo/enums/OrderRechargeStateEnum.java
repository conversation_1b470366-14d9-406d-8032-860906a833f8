package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 充值订单状态
 */
public enum OrderRechargeStateEnum implements ValueEnum {

    /**
     * 已取消
     */
    CANCEL(-1, "已取消"),
    /**
     * 未支付
     */
    UN_PAY(0, "未支付"),
    /**
     * 未提交
     */
    UN_SUBMIT(1, "未提交"),
    /**
     * 充值中
     */
    RECHARGE(2, "充值中"),
    /**
     * 充值成功
     */
    SUCCESS(3, "充值成功"),
    /**
     * 充值失败
     */
    FAILURE(4, "充值失败"),
    /**
     * 已退款
     */
    REFUNDED(5, "已退款"),
    /**
     * 拼团中
     */
    SPELLING(6, "拼团中"),
    /**
     * 待激活
     */
    NOT_ACTIVE(7, "待激活"),
    /**
     * 已过期
     */
    EXPIRED(8, "未激活，已过期"),
    /**
     * 部分成功，部分失败
     */
    PART_SUCCESS_PART_FAILURE(9, "部分成功，部分失败"),
    /**
     * 已作废
     */
    INVALID(10, "已作废"),

    /**
     * 已支付
     */
    PAYED(11, "已支付"),

    /**
     * 退款失败
     */
    REFUND_FAIL(12, "退款失败"),



    ;


    @EnumValue
    private Integer code;
    private String text;

    public static OrderRechargeStateEnum fromCode(Integer code) {
        for (OrderRechargeStateEnum stateEnum : OrderRechargeStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderRechargeStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
