package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 滴滴订电子券状态
 */
public enum EticketStateEnum implements ValueEnum {

    /**
     * 未使用
     */
    UN_USE(0, "未使用"),
    /**
     * 已使用
     */
    USED(1, "已使用"),
    /**
     * 已核销
     */
    CHECKED(2, "已核销");

    @EnumValue
    private Integer code;
    private String text;

    EticketStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
