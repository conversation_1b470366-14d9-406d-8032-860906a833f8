package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_audit_data_record")
public class OrderAuditDataRecord extends BaseEntity {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 手机号
     */
    private String rechargeMobile;

    /**
     * 订单状态
     */
    private String orderState;

    /**
     * 订单类型
     */
    private OrderAuditDataType auditDataType;

    /**
     * 订单类型文本
     */
    private String auditDataTypeStr;


    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单时间
     */
    private Date orderTime;

    /**
     * 所属企业
     */
    private String enterpriseName;

    /**
     * 订单面额
     */
    private BigDecimal orderFace;

    /**
     * 电子券金额
     */
    private BigDecimal couponPayAmount;


    /**
     * 财付通金额
     */
    private BigDecimal mchPayAmount;

    /**
     * 活动金额
     */
    private BigDecimal activityAmount;

    public String getOrderTypeStr(){
        return auditDataType.getText();
    }

}
