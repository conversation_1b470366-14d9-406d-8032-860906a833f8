package cn.joysim.mall.order.model.dto.bm;

import cn.joysim.mall.marketing.model.pojo.enums.MchAccountBalanceType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/12/30
 * @desc: 话费余额
 */
@Data
public class OrderRechargeAccountBalanceDTO implements Serializable {

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 账号
     */
    private String account;

    /**
     * 平台
     */
    private MchAccountBalanceType platformType;
}
