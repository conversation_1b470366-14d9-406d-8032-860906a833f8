package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/25
 * 实物订单导出
 */
@Data
public class OrderEntityExportDTO implements Serializable {



    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String id;
    /**
     * 用户ID
     */
    @ExcelProperty("用户ID")
    private String userId;
    /**
     * 产品总金额
     */
    @ExcelProperty("订单产品总金额")
    private BigDecimal productTotalAmount;
    /**
     * 订单总金额
     */
    @ExcelProperty("订单总金额")
    private BigDecimal orderTotalAmount;
    /**
     * 订单优惠总金额
     */
    @ExcelProperty("订单优惠总金额")
    private BigDecimal discountTotalAmount;
    /**
     * 现金支付金额
     */
    @ExcelProperty("订单现金支付总金额")
    private BigDecimal cashTotalAmount;
    /**
     * 订单班点支付总金额
     */
    @ExcelProperty("订单班点支付总金额")
    private BigDecimal allowanceTotalAmount;
    /**
     * 订单总返现金额
     */
    @ExcelProperty("订单总返现金额")
    private BigDecimal obtainTotalAllowance;
    /**
     * 微信优惠金额
     */
    @ExcelProperty("订单微信优惠总金额")
    private BigDecimal wxDiscountAmount;
    /**
     * 总成本
     */
    @ExcelProperty("订单总成本")
    private BigDecimal totalCost;
    /**
     * 下单时间
     */
    @ExcelProperty("下单时间")
    private String orderTime;
    /**
     * skuId
     */
    @ExcelProperty("SKU ID")
    private String skuId;
    /**
     * 结算名称
     */
    @ExcelProperty("结算名称")
    private String skuFinalName;
    /**
     * 数量
     */
    @ExcelProperty("产品数量")
    private Integer quantity;
    /**
     * 销售价
     */
    @ExcelProperty("产品销售价")
    private BigDecimal sellPrice;
    /**
     * 小计
     */
    @ExcelProperty("产品小计")
    private BigDecimal totalAmount;
    /**
     * 优惠金额
     */
    @ExcelProperty("产品优惠金额")
    private BigDecimal discountAmount;
    /**
     * 实付金额
     */
    @ExcelProperty("产品实付金额")
    private BigDecimal paymentAmount;
    /**
     * 成本价
     */
    @ExcelProperty("产品成本价")
    private BigDecimal costTotalAmount;
    /**
     * 下单手机号
     */
    @ExcelProperty("下单手机号")
    private String mobile;
    /**
     * 订单状态
     */
    @ExcelProperty("订单状态")
    private String stateStr;
    /**
     * 签收时间
     */
    @ExcelProperty(value = "签收时间", converter = ConverterDateTime.class)
    private Date signTime;
    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    private String supplierName;
    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
    /**
     * 备注
     */
    @ExcelProperty("优惠券")
    private String couponInfo;
//    /**
//     * 优惠券面额
//     */
//    @ExcelProperty("优惠券面额")
//    private String couponFaceValue;
    /**
     * 成团时间
     */
    @ExcelProperty(value = "成团时间",converter = ConverterDateTime.class)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date successTime;
    /**
     * 微信优惠券
     */
    @ExcelProperty("微信优惠券")
    private String wxCoupon;
    /**
     * 支付单号
     */
    @ExcelProperty("支付单号")
    private String paymentOrderId;

    /**
     *
     * 银联流水号
     **/
    @ExcelProperty("银联流水号")
    private String  unionOrderId;

    /**
     * 景心优惠
     */
    @ExcelProperty("景心优惠")
    private String joysimPreferential;
    /**
     * 商户贴息
     */
    @ExcelProperty("商户贴息")
    private String joysimAccrual;
    /**
     * 分期情况
     */
    @ExcelProperty("分期情况")
    private String stageDetail;
    /**
     * 银行卡号
     */
    @ExcelProperty("银行卡号")
    private String accNo;
    /**
     * 客服备注
     */
    @ExcelProperty("客服备注")
    private String serviceRemark;

    /**
     * 活动id
     */
    @ExcelProperty("活动id")
    private String activityId;

    /**
     * 活动名称
     */
    @ExcelProperty("活动名字")
    private String activityName;
}
