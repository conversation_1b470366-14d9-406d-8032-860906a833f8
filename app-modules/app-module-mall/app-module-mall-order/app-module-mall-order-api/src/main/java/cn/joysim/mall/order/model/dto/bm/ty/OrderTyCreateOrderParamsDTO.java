package cn.joysim.mall.order.model.dto.bm.ty;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/3
 * @desc:  团油下单参数
 */
@Data
public class OrderTyCreateOrderParamsDTO implements Serializable {

    /**
     * 能链订单号
     */
    private String orderNo;
    /**
     * 油站名称
     */
    private String gasName;
    /**
     * 油站地址
     */
    private String gasAddress;
    /**
     * 订单所有状态 0-待⽀付 1-已⽀付 2-退款中 4-订单取消
     * 5-已退款 7-退款失败
     * * 当前状态 0 待⽀付
     *
     */
    private Integer orderStatus;
    /**
     * 下单时间
     */
    private String orderTime;
    /**
     * 油号
     */
    private String oilNo;
    /**
     * 油号名称
     */
    private String oilName;
    /**
     * 油枪号
     */
    private String gunNo;
    /**
     * 加油升数
     */
    private String num;
    /**
     * 枪价
     */
    private String gunPrice;
    /**
     * ⽤⼾实际加油单价
     */
    private String userRealPrice;
    /**
     * 企业实际⽀付单价
     */
    private String enterpriseRealPrice;
    /**
     * 订单⾦额---⽤⼾输⼊的加油⾦额
     */
    private String totalOrderAmount;
    /**
     * ⽤⼾总实付⾦额
     */
    private String totalUserRealAmout;
    /**
     * 企业总实扣⾦额
     */
    private String totalEnterpriseRealAmount;
    /**
     * ⽤⼾总优惠⾦额
     */
    private String totalUserDiscountAmount;
    /**
     * 企业总优惠⾦额
     */
    private String totalEnterpriseDiscountAmount;
    /**
     * 员⼯⽤⼾Id
     */
    private String userId;
    /**
     * 请求幂等流⽔号
     */
    private String uuid;
    /**
     * 授权码的透传的来源，⼤部分情况不需要，为空
     */
    private String source;
    /**
     * 业务接⼝类型（1=订单推送，2=⽀付推送，3=退款回
     * 告，4=标准收银台）⽤于同⼀url推送不同业务场景
     */
    private String bizType;

    /**
     * 跳转详情⻚的url（使⽤该url可以直接调整到详情⻚）
     */
    private String detailUrl;

    /**
     * 报价快照id
     */
    private String snapshotPriceId;

    /**
     * 油站ID
     */
    private String gasId;

}
