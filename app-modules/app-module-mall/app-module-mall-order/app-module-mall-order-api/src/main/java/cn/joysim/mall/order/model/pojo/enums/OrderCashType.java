package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/5
 * @desc: 提现订单类型
 */
public enum OrderCashType  implements ValueEnum {

    /**
     * 红包
     */
    RED_PACKAGE(0, "红包"),
    /**
     * 津贴提现
     */
    ALLOWANCE(1, "津贴提现"),
    ;

    OrderCashType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderTypeEnum fromCode(Integer code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}


