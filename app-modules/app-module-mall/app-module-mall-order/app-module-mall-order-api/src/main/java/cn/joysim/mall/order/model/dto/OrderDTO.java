package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;
    /**
     * 订单
     */
    private OrderMainDTO orderMain;
    /**
     * 实物订单明细
     */
    private List<OrderEntityDetailDTO> orderDetailList;
    /**
     * 电子券订单明细
     */
    private List<OrderTicketDetailDTO> orderTicketDetail;
    /**
     * 充值订单明细
     */
    private OrderRechargeDetailDTO orderRechargeDetail;
    /**
     * 订单操作日志
     */
    private List<OrderLogDTO> orderLogList;
    /**
     * 收货信息
     */
    private OrderReceiverInfoDTO orderReceiverInfo;
    /**
     * 支付信息
     */
    private OrderPaymentDocumentDTO orderPaymentDocument;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 卡券发送记录
     */
    private List<OrderTicketRecordSendImportDTO> recordSends;


    /**
     * 活动信息
     */
    private OrderActivityDetailDTO activityDetail;
}
