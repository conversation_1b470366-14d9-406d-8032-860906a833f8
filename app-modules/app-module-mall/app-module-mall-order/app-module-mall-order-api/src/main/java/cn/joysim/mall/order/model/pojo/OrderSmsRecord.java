package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderSmsState;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/6/8
 * @desc: 订单短信记录
 */
@Data
@EqualsAndHashCode
@TableName("tb_mall_order_sms_record")
public class OrderSmsRecord extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * jms发送返回的id
     */
    private String msgId;

    /**
     * 短信状态
     */
    private OrderSmsState smsState;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送的短信内容
     */
    private String smsContext;
}
