package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingBankActivityDTO;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivitySkuDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderSaveInsDataFreshDTO {

    private MarketingBankActivitySkuDTO skuDTO;


    private MarketingBankActivityDTO bankActivity;

}
