package cn.joysim.mall.order.model.dto.bm.ty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/3
 * @desc: 团油订单结算页参数
 */
@Data
public class OrderBmTySettleParamDTO implements Serializable {

    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 使用津贴
     */
    private Boolean useAllowance;

    /**
     * 用户Id
     */
    private Long userId;
}
