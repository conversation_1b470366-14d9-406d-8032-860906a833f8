package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.query.OrderEjyCreateOrderQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/03/11
 * @desc 易加油创建订返回参数
 */
@Data
public class OrderEjyCreateOrderDTO implements Serializable {

    /**
     * 易加油订单标识
     */
    private String orderSign;
    /**
     * 油量，单位升，保留两位小数
     */
    private BigDecimal oilMass;
    /**
     * 用户支付金额，保留两位小数
     */
    private BigDecimal payAmount;
    /**
     * 用户最终享受单价
     */
    private String discountPrice;
    /**
     * 优惠券（若有使用优惠券）优惠金额，保留两位小数
     */
    private BigDecimal couponAmount;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 订单总金额，即用户输入金额，保留两位小数
     */
    private String totalAmount;

    /**
     * 易加油下单参数
     */
    private OrderEjyCreateOrderQuery ejyCreateOrderParam;

    /**
     * 油站名称
     */
    private String stationName;
    /**
     * 油号,示例92#
     */
    private String oilCode;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 津贴支付总金额
     */
    private BigDecimal allowanceTotalAmount;

    //每种支付方式，可以抵扣额度
    Map<OrderPaymentType, BigDecimal> paymentTypeAmount = new HashMap<>();

    /**
     * 使用津贴
     */
    private Boolean useAllowance;

    /**
     * 城市名称
     */
    String cityName;

    /**
     * 省份
     */
    String provinceName;

    /**
     * 用户支付金额，保留两位小数(缓存)
     */
    private BigDecimal originalPayAmount;

    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;
}
