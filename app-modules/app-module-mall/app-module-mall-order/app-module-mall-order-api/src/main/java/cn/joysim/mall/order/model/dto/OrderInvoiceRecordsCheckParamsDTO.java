package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.OrderMain;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/10/21
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderInvoiceRecordsCheckParamsDTO implements Serializable {


    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;


    /**
     * 用户id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long userId;

    private OrderMain orderMain;

}
