package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @date 2022/7/25 9:45
 */
public enum CorpTagMathSymbol implements ValueEnum {
    /**
     * 大于等于
     */
    GE(0,"大于等于"),
    /**
     * 大于
     */
    GT(1, "大于"),
    /**
     * 等于
     */
    EQ(2, "等于"),
    /**
     * 小于等于
     */
    LE(3, "小于等于"),
    /**
     * 小于
     */
    LT(4,"小于")
    ;


    CorpTagMathSymbol(Integer code, String text){
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;

    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static CorpTagMathSymbol fromCode(Integer code) {
        for (CorpTagMathSymbol value : CorpTagMathSymbol.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
