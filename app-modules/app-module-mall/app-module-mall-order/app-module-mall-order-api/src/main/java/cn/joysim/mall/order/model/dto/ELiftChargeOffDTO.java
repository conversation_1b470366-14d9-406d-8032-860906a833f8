package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderELifeCouponState;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NonNull;
import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/9 10:57
 */
@Data
@GroupSequenceProvider(ELiftChargeOffDTO.ELiftChargeOffDTOGroupSequenceProvider.class)
public class ELiftChargeOffDTO {

    /**
     * 班马订单ID
     */
    //@NotBlank(message = "工银E生活回调内容有误")
    private String orderId;

    /**
     * 状态(0-未使用、1-已使用)
     */
    private OrderELifeCouponState state;

    /**
     * 使用时间
     */
    @NotNull(message = "工银E生活回调使用时间有误",groups = WhenStateIsUSED.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date useTime;


    /**
     * 使用时间
     */
    @NotNull(message = "工银E生活回调过期时间有误",groups = WhenStateIsEXPIRE.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;


    /**
     * 业务订单号
     */
    private Long bizId;

    /**
     * 字段state等于USED时校验
     */
    public interface WhenStateIsUSED { }

    /**
     * 字段state等于EXPIRE时校验
     */
    public interface WhenStateIsEXPIRE{}


    /**
     *分支校验逻辑
     */
    public static class ELiftChargeOffDTOGroupSequenceProvider implements DefaultGroupSequenceProvider<ELiftChargeOffDTO> {
        @Override
        public List<Class<?>> getValidationGroups(ELiftChargeOffDTO bean) {
            List<Class<?>> defaultGroupSequence = Lists.newArrayList(ELiftChargeOffDTO.class);
            if (bean != null) {
                if (OrderELifeCouponState.USED.equals(bean.getState())) {
                    defaultGroupSequence.add(ELiftChargeOffDTO.WhenStateIsUSED.class);
                }
                if (OrderELifeCouponState.EXPIRE.equals(bean.getState())) {
                    defaultGroupSequence.add(ELiftChargeOffDTO.WhenStateIsEXPIRE.class);
                }
            }
            return defaultGroupSequence;
        }
    }
}
