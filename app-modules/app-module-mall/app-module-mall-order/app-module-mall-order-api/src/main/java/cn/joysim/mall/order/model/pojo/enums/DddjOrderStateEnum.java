package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 滴滴订单状态
 */
public enum DddjOrderStateEnum implements ValueEnum {

    /**
     * 未免登
     */
    UN_LOGIN(0, "未免登"),
    /**
     * 已免登
     * 弃用，每次都重新免登
     */
    LOGINED(1, "已免登"),
    /**
     * 下单中
     */
    ORDERING(2, "下单中"),
    /**
     * 已完成
     */
    FINISHED(3, "已完成"),
    /**
     * 已取消
     */
    CANCEL(-1, "已取消");

    @EnumValue
    private Integer code;
    private String text;

    DddjOrderStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
