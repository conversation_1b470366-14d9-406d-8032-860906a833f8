package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.AppConst;
import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import org.springframework.util.StringUtils;

/**
 * 支付卡类型
 *
 * <AUTHOR>
 * @date 2021/2/1
 */
public enum OrderCardType implements ValueEnum {


    /**
     * 中行借记卡
     */
    BOC_DEBIT_CARD(0, "中行借记卡", "01"),
    /**
     * 中行信用卡、分行
     */
    BOC_CREDIT_CARD_BRANCH(1, "中行信用卡", "02"),
    /**
     * 中行信用卡、总行
     */
    BOC_CREDIT_CARD_HEAD(2, "中行信用卡", "04"),

    /**
     * 银联借记卡
     */
    UNION_PAY_DEBIT_CARD(3, "银联借记卡", "11"),
    /**
     * VISA借记卡
     */
    VISA_DEBIT_CARD(4, "VISA借记卡", "21"),
    /**
     * VISA信用卡
     */
    VISA_CREDIT_CARD(5, "VISA信用卡", "22"),
    /**
     * MC借记卡
     */
    MC_DEBIT_CARD(6, "MC借记卡", "31"),
    /**
     * MC信用卡
     */
    MC_CREDIT_CARD(7, "MC信用卡", "32"),
    /**
     * 运通卡
     */
    AMERICAN_EXPRESS_CARD(8, "运通卡", "42"),
    /**
     * 大来卡
     */
    DINERS_CLUB_CARD(9, "大来卡", "52"),
    /**
     * JCB卡
     */
    JCB_CARD(10, "JCB卡", "62"),
    /**
     * 他行卡
     */
    OTHER_CARD(11, "他行卡", "UP"),
    /**
     * 银联微信
     */
    WX_UNION_CARD(12, "银联微信", "WX"),
    /**
     * 数字人民币
     */
    ECNY(13, "数字人民币", "EP"),
    ;


    OrderCardType(Integer code, String text, String typeCode) {
        this.code = code;
        this.text = text;
        this.typeCode = typeCode;
    }

    @EnumValue
    private Integer code;
    private String text;
    private String typeCode;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public static String fromTypeCode(String typeCode) {
        if (StringUtils.hasText(typeCode)) {
            for (OrderCardType orderCardType : OrderCardType.values()) {
                if (typeCode.contains(",")) {
                    String[] split = typeCode.split(",");
                    if (split.length > 0) {
                        StringBuilder stringBuilder = new StringBuilder("");
                        for (String temp : split) {
                            if (orderCardType.getTypeCode().equals(temp)) {
                                stringBuilder.append(orderCardType.getText());
                            }
                        }
                        return stringBuilder.toString();
                    }
                } else {
                    if (orderCardType.getTypeCode().equals(typeCode)) {
                        return orderCardType.getText();
                    }
                }

            }
        }
        return typeCode;
    }

}
