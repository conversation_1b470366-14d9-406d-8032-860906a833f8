package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.DddjOrderStateEnum;
import cn.joysim.mall.order.model.pojo.enums.EticketStateEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 滴滴代驾订单
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_dddj_order")
public class DddjOrder{

    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 滴滴订单状态
     */
    private DddjOrderStateEnum state;

    /**
     * 电子券使用状态
     */
    private EticketStateEnum eticketState;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 本地库的券id（EsmEticket）
     */
    private Long ticketId;

    /**
     * 券码
     */
    private String code;

    /**
     * 滴滴订单号(外部)
     */
    private String ddOrderNo;

    /**
     * 滴滴token用于免登
     */
    private String ddToken;

    /**
     * 滴滴pid用于免登
     */
    private String ddPid;

    /**
     * 权益ID
     */
    private String privilegeId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
