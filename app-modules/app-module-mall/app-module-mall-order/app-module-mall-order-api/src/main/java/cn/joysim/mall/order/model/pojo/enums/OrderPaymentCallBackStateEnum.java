package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 订单支付回调
 */
public enum OrderPaymentCallBackStateEnum implements ValueEnum {

    /**
     * 未回调
     */
    UN_CALL_BACK(0, "未回调"),
    /**
     * 回调成功
     */
    CALL_BACK_SUCCESS(1, "回调成功");

    OrderPaymentCallBackStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderPaymentCallBackStateEnum fromCode(Integer code) {
        for (OrderPaymentCallBackStateEnum orderTypeEnum : OrderPaymentCallBackStateEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
