package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.MarketingBankActivitySku;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.product.model.pojo.*;
import lombok.Data;

@Data
public class OrderInsSKUSaveDTO {



    private Integer quantity;

    private MarketingBankActivitySku marketingBankActivitySku;


    private ProductSKU sku;

    private ProductSPU productSpu;


    private Category category;

    private ProductSupplier productSupplier;


    private MallMchSetting mchSettingBySpuId;

    private OrderInsBankDTO inputData;


}
