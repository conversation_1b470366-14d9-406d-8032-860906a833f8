package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderReceiverInfoDTO implements Serializable {

    /**
     * 收货人
     */
    private String receiverName;
    /**
     * 收货地址
     */
    private String receiverAddress;
    /**
     * 收货人手机号码
     */
    private String receiverMobile;
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 物流单号
     */
    private String logisticsNum;
    /**
     * 签收时间
     */
    private Date signTime;
    /**
     * 发货时间
     */
    private Date sendTime;
    /**
     * 邮费
     */
    private BigDecimal postage;

}
