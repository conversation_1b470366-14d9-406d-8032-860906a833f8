package cn.joysim.mall.order.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/11
 * @desc 创建订单请求参数
 */
@Data
public class OrderEjyStationPriceInfoQuery implements Serializable {

    /**
     * 油站 ID
     */
    @NotBlank(message = "油站id必填")
    private String stationId;

    /**
     * 用户手机号码
     */
    @NotBlank(message = "用户手机号码必填")
    private String userPhone;

}
