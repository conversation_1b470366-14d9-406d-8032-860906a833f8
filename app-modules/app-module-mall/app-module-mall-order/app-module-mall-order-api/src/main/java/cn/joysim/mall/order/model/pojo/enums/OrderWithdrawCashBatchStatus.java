package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/4
 * @desc: 提现订单批次状态
 */
public enum OrderWithdrawCashBatchStatus implements ValueEnum {

    /**
     * 未提交
     */
    UNTREATED(0,"未提交"),
    /**
     * 待付款确认
     */
    WAIT_PAY(1, "待付款确认"),

    /**
     * 已受理
     */
    ACCEPTED(2, "已受理"),

    /**
     * 转账中
     */
    PROCESSING(3, "转账中"),

    /**
     * 已完成
     */
    FINISHED(4, "已完成"),

    /**
     * 已关闭
     */
    CLOSED(5,"已关闭"),

    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderWithdrawCashBatchStatus(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}


