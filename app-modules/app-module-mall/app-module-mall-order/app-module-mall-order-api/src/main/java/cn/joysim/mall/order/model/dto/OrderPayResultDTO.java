package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.dto.boc.OrderBocPayDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微信预下单结果
 * <AUTHOR>
 * @date 2019/10/30
 */
@Data
public class OrderPayResultDTO implements Serializable {

    /**
     * 小程序Id
     */
    private String appId;
    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 数据包
     */
    private String packageStr;
    /**
     * 签名类型
     */
    private String signType;
    /**
     * 支付签名
     */
    private String paySign;


    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 支付金额
     */
    private BigDecimal paymentOrderAmount;
    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 申码结果
     */
    /**
     * 二维码链接
     */
    private String qrNo;

    /**
     * 二维码图片
     */
    private String qrImage;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 中国银行支付信息
     */
    private OrderBocPayDTO orderBocPayDTO;

}
