package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 券补发记录
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tb_mall_order_ticket_reissue_record")
public class OrderTicketReissueRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 发券记录id
     */
    private Long ticketSendRecordId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 补发前券产品条码
     */
    private String beforeReissueBarCode;

    /**
     * 补发后券产品条码
     */
    private String afterReissueBarCode;

    /**
     * 发券结果
     */
    private String result;

    /**
     * 创建人
     */
    private String createUser;



}
