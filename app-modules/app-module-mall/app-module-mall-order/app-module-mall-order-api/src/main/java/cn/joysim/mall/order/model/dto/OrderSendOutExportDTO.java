package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/11/5
 * 订单发货表导出
 */
@Data
public class OrderSendOutExportDTO implements Serializable {

    @ExcelProperty("活动名字")
    private String activityName;

    @ExcelProperty("活动id")
    private String activityId;
    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String id;
    /**
     * 商品信息
     */
    @ExcelProperty("商品")
    private String sku;
    /**
     * 下单时间
     */
    @ExcelProperty("下单时间")
    private String orderTime;
    /**
     * 支付时间
     */
    @ExcelProperty("支付时间")
    private String paymentTime;
    /**
     * 下单手机号
     */
    @ExcelProperty("下单手机号")
    private String mobile;
    /**
     * 订单金额
     */
    @ExcelProperty("订单金额")
    private BigDecimal orderTotalAmount;
    /**
     * 实付金额
     */
    @ExcelProperty("实付金额")
    private BigDecimal paymentTotalAmount;
    /**
     * 订单状态
     */
    @ExcelProperty("订单状态")
    private String stateStr;
    /**
     * 收件人
     */
    @ExcelProperty("收件人")
    private String receiverName;
    /**
     * 收货号码
     */
    @ExcelProperty("收货号码")
    private String receiverMobile;
    /**
     * 收货地址
     */
    @ExcelProperty("收货地址")
    private String receiverAddress;
    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    private String supplierName;
    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}
