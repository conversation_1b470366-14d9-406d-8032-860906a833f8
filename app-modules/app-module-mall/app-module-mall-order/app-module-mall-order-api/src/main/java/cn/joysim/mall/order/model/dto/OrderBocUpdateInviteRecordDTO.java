package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingInviteState;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/1/9
 * @desc: 写入邀请记录
 */
@Data
@Accessors(chain = true)
public class OrderBocUpdateInviteRecordDTO implements Serializable {
    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 邀请者的用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviterUserId;

    /**
     * 被邀请的用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeUserId;

    /**
     * 被邀请人的支付校验订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeOrderId;

    /**
     * 被邀请人手机号
     */
    private String inviteeMobile;


    /**
     * 银行活动次数来源
     */
    private MarketingBankActivityNumSourceEnum activityNumSource;


    /**
     * 用户下单请求ip
     */
    private String orderIp;
}
