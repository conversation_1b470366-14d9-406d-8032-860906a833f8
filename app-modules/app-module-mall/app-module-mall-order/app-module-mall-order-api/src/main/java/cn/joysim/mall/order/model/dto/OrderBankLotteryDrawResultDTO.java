package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingPrizeGrantType;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import cn.joysim.mall.marketing.model.vo.MarketBankActivityMiniAppPrizeVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @version: 2.5.5
 * @author: linhs
 * @date: 2022/4/26
 * @desc: 抽奖结果
 */
@Data
public class OrderBankLotteryDrawResultDTO implements Serializable {

    /**
     * 是否中奖
     */
    private Boolean winning;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 结果
     */
    private String resultMes;

    /**
     * 是否指定卡
     */
    private Boolean flag;

    /**
     * 中奖时中奖奖品的图片
     */
    private String imgUrl;

    /**
     * 按钮名称
     */
    private String buttonName;

    /**
     * 是否内部路径
     * ture 内部路径
     * false外部链接
     */
    private Boolean isInsideUrl;
    /**
     * 跳转路径
     */
    private String jumpPath;

    /**
     * 购买提示语
     */
    private String markedWords;

    /**
     * appId
     */
    private String appId;

    /**
     * 奖品发放形式
     */
    private MarketingPrizeGrantType grantType;

    /**
     * 奖品sku记录id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long prizeSkuRecordId;


    /**
     * 扩展字段
     */
    private List<MarketBankActivityMiniAppPrizeVo.CardTypesInfo> cardTypes;

    /**
     * 奖品的sku名称
     */
    private String activitySkuName;

    /**
     * 奖品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prizeId;

    /**
     * 订单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 电子券类型/奖品类型
     * {@link cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum}
     */
    private Integer ticketType;

    /**
     * 银行商城URL
     */
    private String bankUrl;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;

    /**
     * 到账手机号
     */
    private String rechargeMobile;

    /**
     * 是否支付宝选择账号
     */
    private Boolean alipaySelectAccount;
}
