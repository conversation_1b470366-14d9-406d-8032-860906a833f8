package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 2.0.3
 * @author: linhs
 * @date: 2020/8/7
 * @desc: 邀请订单类型
 */
public enum  OrderInviteType implements ValueEnum {

    /**
     * 分销邀请
     */
    DISTRIBUTION_INVITE(0, "分销邀请"),
    /**
     * 任务邀请
     */
    MISSION_INVITE(1, "任务邀请"),

    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderInviteType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}

