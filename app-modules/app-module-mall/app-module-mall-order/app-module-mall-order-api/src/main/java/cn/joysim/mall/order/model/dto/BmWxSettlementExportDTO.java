package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:结算单第三方支付宝导出DTO
 * @Author: LiuHW
 * @date: 2024/5/22 15:51
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class BmWxSettlementExportDTO implements Serializable {

    /**
     * 批次id
     */
    @ExcelProperty(value = "批次id")
    private String batchId;

    /**
     * 券ID
     */
    @ExcelProperty(value = "券ID")
    private String couponId;

    /**
     * 优惠类型
     */
    @ExcelProperty(value = "优惠类型")
    private String discountType;

    /**
     * 优惠金额（核销面额）
     */
    @ExcelProperty(value = "优惠金额（核销面额）")
    private BigDecimal discountAmount;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private BigDecimal orderTotalAmount;

    /**
     * 账单类型（交易类型）0:未知 1:支付 2:退款
     */
    @ExcelProperty(value = "账单类型")
    private String billType;

    /**
     * 支付单号（支付宝交易号）
     */
    @ExcelProperty(value = "支付单号")
    private String paymentOrderNumber;

    /**
     * 消费时间（核销时间）
     */
    @ExcelProperty(value = "消费时间")
    private String consumerTime;

    /**
     * 消费商户号
     */
    @ExcelProperty(value = "消费商户号")
    private String consumerBusinessAccount;


    /**
     * 设备号
     */
    @ExcelProperty(value = "设备号")
    private String deviceNumber;

    /**
     * 银行返回流水号
     */
    @ExcelProperty(value = "银行返回流水号")
    private String bankResponseSerialNumber;

    /**
     * 消耗门店编码(微信支付)
     */
    @ExcelProperty(value = "消耗门店编码(微信支付)")
    private String wechatConsumerStoreCode;

    /**
     * 消耗门店编码(商家自有)
     */
    @ExcelProperty(value = "消耗门店编码(商家自有)")
    private String businessConsumerStoreCode;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String mobile;

    /**
     * esm券码
     */
    @ExcelProperty(value = "esm券码")
    private String esmCode;
}
