package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/03/15
 * 油站广告
 */
@Data
@TableName("tb_mall_order_ejy_station_advert")
public class OrderEjyStationAdvert extends BaseEntity {

    /**
     * 油站id
     */
    String stationId;

    /**
     * id，活动标签的唯一标识
     */
    String advertId;

    /**
     * 权重，999 为最高权重
     */
    String weight;

    /**
     * 单字图标文案
     */
    int oilType;

    /**
     * 图标内文案
     */
    String singleWordIcon;

    /**
     * 图标后文案
     */
    String titleWithinIcon;
}
