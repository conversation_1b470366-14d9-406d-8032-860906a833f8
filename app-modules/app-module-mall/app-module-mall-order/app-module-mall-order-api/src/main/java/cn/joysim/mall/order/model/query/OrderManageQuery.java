package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/06
 */
@Data
public class OrderManageQuery implements Serializable {

    /**
     *订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     *支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     *订单类型
     */
    private Integer orderType;
    /**
     *支付手机号
     */
    private String mobile;
    /**
     *供应商
     */
    private String supplierName;

    /**
     * 查询开始时间
     * "yyyy-MM-dd"
     */
    private String startDate;

    /**
     * 查询结束时间
     * "yyyy-MM-dd"
     */
    private String endDate;


    /**
     * 兑换专区ID
     */
    @Pattern(regexp = "\\d*",message = "兑换专区ID不正确")
    private String exchangeId;

    /**
     *企业id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long esmCorpId;


}
