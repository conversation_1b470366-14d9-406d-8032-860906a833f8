package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.RechargeSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 充值订单
 */
@Data
public class OrderRechargeDTO implements Serializable {

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /***
     * 用户Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long userId;
    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 支付现金
     */
    private BigDecimal cashTotalAmount;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 充值类型
     */
    private RechargeTypeEnum rechargeType;
    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 供应商
     * 0：欧飞
     * 1：分销平台
     * 2：正联
     * 3：方知
     */
    private RechargeSupplierEnum rechargeSupplier;
    /**
     * 子订单状态
     */
    private Integer subState;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 备注
     */
    private String remark;
    /**
     * 成本
     */
    private BigDecimal totalCost;
    /**
     * 班点支付金额
     */
    private BigDecimal allowanceTotalAmount;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 是否银行活动订单
     * true：是
     * false：否
     */
    private Boolean bankActivityOrder;

    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;

    /**
     * 供应商名称
     */
    private String rechargeSupplierName;


    /**
     * 客服备注
     */
    private String serviceRemark;

    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;


    /**
     * 兑换专区
     */
    private String exchangeId;
}
