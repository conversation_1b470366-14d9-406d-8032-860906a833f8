package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class PolymerizeOrderRecordDTO {


    /**
     * sku的图片
     */
    private String imgUrl;
    /**
     * sku名称
     */
    private String skuName;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单状态
     */
    private String state;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;


}
