package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡券订单拆分发券记录
 *
 * <AUTHOR>
 * @date 2023-03-07
 */
@Data
public class OrderTicketRecordSendDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 卡券订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderTicketDetailId;

    /**
     * 活动记录Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityRecordId;

    /**
     * 银行活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bankActivityId;


    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 组合skuId（活动产品的skuId）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long composeSkuId;

    /**
     * 组合sku名称（活动产品的sku名称）
     */
    private String composeSkuName;

    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    /**
     * skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;


    /**
     * 卡券类型类型
     */
    private TicketTypeEnum ticketType;

    /**
     * sku结算名称
     */
    private String skuFinalName;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 面额
     */
    private Integer amount;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 订单状态
     */
    private TicketStateEnum state;

    /**
     * 发券时间
     */
    private Date sendTime;

    /**
     * 充值编码
     */
    private String chargeCode;

    /**
     * 发券结果信息
     */
    private String message;

    /**
     * 发券结果信息
     */
    private String ticketInfo;

    /**
     * 批次Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long batchId;

    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 面值
     */
    private BigDecimal faceValue;

    /**
     * 销售价
     */
    private BigDecimal sellPrice;

    /**
     * 充供应商
     */
    private TicketSupplierEnum ticketSupplier;

    /**
     * 油卡id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long oilCardId;

    /**
     * openid
     */
    private String openId;

    /**
     * 奖品发放形式,0:直接发放,1:客户自行领取
     */
    private Integer grantType;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 消费时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date wxUseTime;

    /**
     * 下单时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date orderTime;


    /**
     * 组合礼包sku排序
     */
    private Integer composeSort;

    /**
     * 补发Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long resentId;


    /**
     * 券id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long couponId;


    /**
     * 发送电子券时间
     */
    private Date sendTicketTime;


    /**
     * 主订单状态
     */
    /**
     * 订单状态
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderEntityStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderRechargeStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderEjyStateEnum}
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderTyStateEnum}
     */
    private Integer OrderMainState;

}
