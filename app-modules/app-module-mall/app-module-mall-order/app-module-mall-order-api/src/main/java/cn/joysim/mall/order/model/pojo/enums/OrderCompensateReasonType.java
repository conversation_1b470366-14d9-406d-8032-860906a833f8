package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
public enum OrderCompensateReasonType implements ValueEnum {

    COUPON_EXPIRE(0,"原券过期"),

    OMIT(1,"漏发"),

    ACTIVITY_END(2, "活动结束补偿"),

    ACCIDENT(3, "未享受到支付宝/微信/美团满减"),

    OTHER(4, "其他"),

    SKU_NOT_ENOUGH(5,"库存不足补发"),

    MOBILE_CHANGE(6,"手机号变更无法收券");

    OrderCompensateReasonType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static OrderCompensateReasonType fromCode(Integer code) {
        for (OrderCompensateReasonType value : OrderCompensateReasonType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
