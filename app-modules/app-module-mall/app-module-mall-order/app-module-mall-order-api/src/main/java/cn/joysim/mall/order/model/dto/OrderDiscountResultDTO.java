package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by zhuzl on 2020/2/26.
 */
@Data
public class OrderDiscountResultDTO implements Serializable {
    /**
     * 优惠券优惠总金额
     */
    private BigDecimal couponDiscountTotalAmount;
    /**
     * 已选优惠券数量
     */
    private Integer useCouponNum;
    /**
     * 使用的优惠券记录
     * key:优惠券Id
     * value:用户的优惠券记录
     */
    private Map<Long, List<Long>> couponRecordMap;
    /**
     * 使用的优惠券对应的商品
     * key:优惠券Id
     * value:优惠券对应的商品
     */
    private Map<Long, List<Long>> couponSkuMap;
    /**
     * 优惠券的每个商品优惠金额
     * key:skuId
     * value:商品的优惠金额
     */
    private Map<Long, BigDecimal> couponSkuDiscountAmountMap;
}
