package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/11/10
 * 退款状态
 */
public enum OrderRefundStateEnum implements ValueEnum {

    /**
     * 未申请
     */
    UN_APPLY(0, "未申请"),
    /**
     * 申请成功
     */
    APPLY_SUCCESS(1, "申请成功"),
    /**
     * 申请失败
     */
    APPLY_FAIL(2, "申请失败"),
    /**
     * 退款成功
     */
    SUCCESS(3, "退款成功"),
    /**
     * 退款异常
     */
    FAIL(4, "退款失败");

    OrderRefundStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
