package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version: *******
 * @author: linhs
 * @date: 2021/7/6
 * @desc: 车主卡券可升级列表
 */
@Data
public class CzfwTicketUngradeableDTO implements Serializable {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 权益名称
     */
    private String productName;

    /**
     * 需要补偿的差价
     */
    private Double compensationPrice;

    /**
     * 面值
     */
    private Integer price;

    /**
     * 权益说明
     */
    private String description;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 权益类型
     */
    private Integer categoryCode;
    /**
     * 权益类型名称
     */
    private String category;
    /**
     * 权益子类型
     */
    private Integer categorySubCode;
    /**
     * 权益子类型名称
     */
    private String categorySub;

    /**
     * 门店
     */
    private Map<String, Map<String, List<CzfwTicketDetailCombileDTO.Shop>>> shops;

}
