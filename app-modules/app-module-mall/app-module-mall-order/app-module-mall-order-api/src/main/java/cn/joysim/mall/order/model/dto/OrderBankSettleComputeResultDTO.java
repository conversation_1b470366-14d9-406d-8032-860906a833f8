package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.bank.MarketingBankActivityCouponItemDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/5/15
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderBankSettleComputeResultDTO implements Serializable {

    /**
     * 优惠金额
     */
    private BigDecimal couponValue;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 使用优惠券张数
     */
    private Integer useNum;

    /**
     * 优惠券总张数
     */
    private Integer totalNum;

    /**
     * 使用的银行单品券
     */
    List<MarketingBankActivityCouponItemDTO> useSingleCoupons;
}
