package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.OrderEjyOilCodeEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderEjyStationDistanceEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderEjyStationOrderType;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: zheng<PERSON>
 * @date: 2022/3/16
 * @desc: 易加油油站列表查询
 */
@Data
public class OrderEjyStationSearchQuery implements Serializable {


    /**
     * 距离范围
     * 0：6km
     * 1: 10km
     * 2: 15km
     * 3: 20km
     * 4: 50km
     */
    private OrderEjyStationDistanceEnum distanceType;

    /**
     * 油号类型
     * 0：0#
     * 1: 92#
     * 2: 95#
     * 3: 98#
     */
    private OrderEjyOilCodeEnum oilType;

    /**
     * 排序方式
     * 0：距离优先
     * 1: 价格优先
     * 2: 评分优先
     */
    private OrderEjyStationOrderType sortType;

    /**
     * 纬度，百度坐标系
     */
    String latitude;

    /**
     * 经度，百度坐标系
     */
    String longitude;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 油号类型
     */
    private Integer oilNo;
}
