package cn.joysim.mall.order.model.dto.boc;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description Created with IDEA
 * @create: 2022-07-11 16:14
 * @since JDK 1.8
 */
@Data
public class OrderBocPayDTO implements Serializable {

    /**
     * 商户号（最大20位数字）
     */
    private String merchantNo;

    /**
     * 版本号（固定1.0.1）
     */
    private String version;

    /**
     * 交易码（此接口请上送0000212）
     */
    private String messageId;

    /**
     * 签名方法，固定上送：P7
     */
    private String security;

    /**
     * 请求报文明文信息: 将业务数据组成XML字符串格式进行UTF-8格式的转码
     */
    private String message;

    /**
     * 请求报文签名信息：对报文原文XML字符串格式的字节数组（UTF-8格式）进行签名。此字段不需Base64编码。
     */
    private String signature;
}
