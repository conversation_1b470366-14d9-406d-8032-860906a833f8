package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/28
 * 充值订单
 */
@Data
public class OrderRechargeSaveDTO implements Serializable {

    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 产品skuId
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long skuId;
    /**
     * 充值类型
     */
    private RechargeTypeEnum rechargeType;
    /**
     * 油卡skuId
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long oilSkuId;

    /**
     * 是否拉新活动订单
     */
    private Boolean weComOrder;

    /****************中石化或中石油油卡充值*****************/

    /**
     * 油卡id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long oilCardId;


    /**
     * 购买数量
     */
//    private Integer quantity;
}
