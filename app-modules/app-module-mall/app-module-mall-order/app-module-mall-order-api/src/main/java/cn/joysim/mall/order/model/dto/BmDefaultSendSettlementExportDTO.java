package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.*;
import cn.joysim.mall.marketing.model.pojo.enums.BankActivityMode;
import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.WechatCouponState;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:结算单通用卡券导出DTO
 * @Author: LiuHW
 * @date: 2024/5/22 15:51
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class BmDefaultSendSettlementExportDTO implements Serializable {

    @ExcelProperty(value = "发券记录ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long ticketRecordId;

    @ExcelProperty(value = "关联活动ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long activityId;

    @ExcelProperty(value = "关联活动名称")
    @ColumnWidth(15)
    private String activityName;

    @DeserializeClassType(BankActivityMode.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "活动模式", converter = EnumConverter.class)
    @ColumnWidth(10)
    private BankActivityMode activityMode;

    @ExcelProperty(value = "订单ID", converter = LongStringConverter.class)
    @ColumnWidth(15)
    private Long orderId;

    @DeserializeClassType(OrderTicketStateEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "订单状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private OrderTicketStateEnum orderStatus;

    @ExcelProperty(value = "下单时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date orderTime;

    @ExcelProperty(value = "充值账号")
    @ColumnWidth(10)
    private String rechargeAccount;

    @ExcelProperty(value = "充值openId")
    @ColumnWidth(10)
    private String openId;

    @ExcelProperty(value = "券产品SKUID",converter = LongStringConverter.class)
    @ColumnWidth(10)
    private Long skuId;

    @ExcelProperty(value = "券产品名称")
    @ColumnWidth(15)
    private String skuName;

    @ExcelProperty(value = "关联组合礼包ID",converter = LongStringConverter.class)
    @ColumnWidth(10)
    private Long composeGiftId;

    @ExcelProperty(value = "关联组合礼包名称")
    @ColumnWidth(10)
    private String composeGiftName;

    @ExcelProperty(value = "券产品条码")
    @ColumnWidth(15)
    private String chargeCode;

    @DeserializeClassType(TicketTypeEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "卡券类型", converter = EnumConverter.class)
    @ColumnWidth(10)
    private TicketTypeEnum ticketType;

    @ExcelProperty(value = "产品面额")
    @ColumnWidth(10)
    private BigDecimal faceValue;

    @ExcelProperty(value = "成本")
    @ColumnWidth(10)
    private BigDecimal cost;

    @ExcelProperty(value = "实付金额")
    @ColumnWidth(10)
    private BigDecimal payMoney;

    @DeserializeClassType(TicketStateEnum.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "券记录状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private TicketStateEnum ticketRecordState;

    @ExcelProperty(value = "预发券时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date preSendTicketTime;

    @ExcelProperty(value = "发券时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date sendTicketTime;

    @ExcelProperty(value = "券码")
    @ColumnWidth(10)
    private String ticketCode;

    @DeserializeClassType(WechatCouponState.class)
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "券状态", converter = EnumConverter.class)
    @ColumnWidth(10)
    private WechatCouponState ticketStatus;

    @ExcelProperty(value = "消费时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date consumeTime;

    @ExcelProperty(value = "到期时间",converter = ConverterDateTime.class)
    @ColumnWidth(10)
    private Date expiredTime;

    @ExcelProperty(value = "发券结果")
    @ColumnWidth(15)
    private String sendTicketResult;

    @ExcelProperty(value = "网点二维码ID")
    @ColumnWidth(15)
    private String qrCodeId;

    @ExcelProperty(value = "发券人手机号")
    @ColumnWidth(15)
    private String sendManMobile;

    @ExcelProperty(value = "第三方批次号")
    @ColumnWidth(15)
    private String thirdBatchId;

    @ExcelProperty(value = "第三方券码")
    @ColumnWidth(15)
    private String thirdCouponId;

}
