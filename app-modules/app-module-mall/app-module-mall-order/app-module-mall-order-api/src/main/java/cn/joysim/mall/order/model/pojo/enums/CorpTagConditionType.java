package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @date 2022/7/25 9:48
 */
public enum CorpTagConditionType implements ValueEnum {

    /**
     * 订单数量
     */
    ORDER_NUM(0,"订单数量"),
    /**
     * 金额
     */
    AMOUNT(1, "金额"),
    ;

    CorpTagConditionType(Integer code, String text){
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;

    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static CorpTagConditionType fromCode(Integer code) {
        for (CorpTagConditionType value : CorpTagConditionType.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
