package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 实物订单状态
 */
public enum OrderEjyOrderDetailStatusEnum implements ValueEnum {

    /**
     * 已取消
     */
    PAY(1, "支付"),
    /**
     * 未支付
     */
    REFUND(2, "退款"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEjyOrderDetailStatusEnum fromCode(Integer code) {
        for (OrderEjyOrderDetailStatusEnum stateEnum : OrderEjyOrderDetailStatusEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEjyOrderDetailStatusEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
