package cn.joysim.mall.order.model.query;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 发券记录查询
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
public class OrderTicketSendRecordQuery implements Serializable {

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 组合礼包id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long composeGiftId;

    /**
     * 发券记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ticketRecordId;

    /**
     * 券产品SKU IDs
     */
    private String skuIds;

    /**
     * 券产品SKU IDs List
     */
    private List<Long> skuIdsList;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 券状态
     */
    private Integer ticketStatus;

    /**
     * 发券记录状态
     */
    private Integer ticketRecordStatus;

    /**
     * 下单开始时间
     */
    private Date orderStartTime;

    /**
     * 下单结束时间
     */
    private Date orderEndTime;

    /**
     * 发券开始时间
     */
    private Date sendStartTime;

    /**
     * 发券结束时间
     */
    private Date sendEndTime;

    /**
     * 卡券类型
     */
    private Integer ticketType;

    /**
     * 券码
     */
    private String ticketCode;

    /**
     * 产品条码
     */
    private String chargeCode;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 发券结果
     */
    private String sendTicketResult;

    /**
     * 网点二维码ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long qrCodeId;

    /**
     * 券产品信息-产品名称
     */
    private String skuName;

    /**
     * 下单手机号
     */
    private String activityRecordMobile;


    /**
     * 第三方券码
     */
    private String thirdCouponId;

    /**
     * 是否有领取过期时间
     */
    private Boolean haveExpireDate;
}
