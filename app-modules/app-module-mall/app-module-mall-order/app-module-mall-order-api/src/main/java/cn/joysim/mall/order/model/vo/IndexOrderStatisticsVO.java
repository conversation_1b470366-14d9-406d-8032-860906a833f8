package cn.joysim.mall.order.model.vo;

import cn.joysim.mall.order.model.dto.OrderSwitchDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页订单统计
 *
 * <AUTHOR>
 * @date 2021/3/1
 */
@Data
public class IndexOrderStatisticsVO implements Serializable {

    /**
     * 当天订单总数量
     */
    private BigDecimal orderTotalNum;
    /**
     * 当天订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 当天发放班点总金额
     */
    private BigDecimal issueBanDianTotalAmount;
    /**
     * 当天充值班点总金额
     */
    private BigDecimal rechargeBanDianTotalAmount;
    /**
     * 当天消费班点总金额
     */
    private BigDecimal useBanDianTotalAmount;
    /**
     * 七日客单价
     */
    private BigDecimal perCustomerTransaction;
    /**
     * 七日客均消费
     */
    private BigDecimal perCustomerConsumption;
    /**
     * 付款用户比例
     */
    private Double paymentUserNumberRatio;
    /**
     * 复购率
     */
    private Double repurchaseRate;

    /**
     * 最近七天订单统计
     */
    private IndexOrderStatisticsItemVO orderStatisticsItem;

    /**
     * 订单开关
     */
    private OrderSwitchDTO orderSwitch;
}
