package cn.joysim.mall.order.model.dto;

import cn.joysim.common.utils.BeanUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2021/5/26
 * @desc: 车主服务卡券详情
 */
@Data
public class CzfwTicketDetailDTO implements Serializable {

    /**
     * 电子券Id
     */
    private Integer id;
    /**
     * 电子券权益id
     */
    private Integer eticketProductId;
    /**
     * 企业Id
     */
    private Integer enterpriseId;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 权益名称
     */
    private String productName;
    /**
     * 面值
     */
    private Integer price;
    /**
     * 开始生效时间
     */
    private String startTime;
    /**
     * 过期时间
     */
    private String expiredTime;
    /**
     * 发送时间
     */
    private String sendTime;
    /**
     * 消费类型，1：不指定门店 0：指定
     */
    private Integer consumeType;
    /**
     * 券码
     */
    private String code;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 总可用次数
     */
    private Integer totalTimes;
    /**
     * 已使用次数
     */
    private Integer usedTimes;
    /**
     * 有可消费门店id，以逗号隔开
     */
    private String shopIds;
    /**
     * 有可消费门店id，以逗号隔开
     */
    private Map<String, Object> shops;
    /**
     * 券状态，详情见
     * 【券码状态字典】
     * 0
     * 已发放、正常，可消费
     * 1
     * 已作废，不可消费
     * 2
     * 已消费
     * 3
     * 已过期，不可消费
     * 4
     * 待激活，不可消费
     * 5
     * 已锁定、服务中，可消费
     * 6
     * 已升级，不可消费
     */
    private Integer state;
    /**
     * 权益类型
     */
    private Integer categoryCode;
    /**
     * 权益类型名称
     */
    private String category;
    /**
     * 权益子类型
     */
    private Integer categorySubCode;
    /**
     * 权益子类型名称
     */
    private String categorySub;
    /**
     * 实物礼品权益编号
     */
    private String physicalProductCode;
    /**
     * 是否可评论 = 未评论 && 消费时间7天内
     * <p>
     * 1-是、0-否
     */
    private Integer canEvaluation;
    /**
     * 是否显示卡券面值   0不显示，1显示
     */
    private Integer showMoney;
    /**
     * 消费门店名称Id
     */
    private Integer validateShopId;
    /**
     * 消费门店名称
     */
    private String validateShop;
    /**
     * 消费时间
     */
    private String validateTime;

    /**
     * 权益说明
     */
    private String description;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 预约地址
     */
    private String subscribeUrl;

    //private Map<String, Map<String, List<Shop>>> shops;

    /**
     * 可升级产品列表
     */
    private List<CzfwTicketDetailDTO> upGradeList;

    private Boolean success;

    /**
     * 重新解析后的店铺列表
     */
    private List<CzfwTicketDetailShopDTO> shopList;

    /**
     * 重写 shopList 的 get 方法
     */
    @SuppressWarnings(value = {"unchecked", "unused"})
    public List<CzfwTicketDetailShopDTO> getShopList() {
        if (CollectionUtils.isEmpty(this.shops)) {
            return Lists.newArrayListWithExpectedSize(1);
        }
        if (!CollectionUtils.isEmpty(this.shopList)) {
            return shopList;
        }

        // 重构解析
        Set<CzfwTicketDetailShopDTO> sets = Sets.newConcurrentHashSet();
        try {
            for (Map.Entry<String, Object> ent : this.shops.entrySet()) {

                Map<String, Object> tmpMap = (Map<String, Object>) ent.getValue();
                if (Objects.nonNull(tmpMap)) {

                    for (Map.Entry<String, Object> e : tmpMap.entrySet()) {
                        List<Object> list = (List<Object>) e.getValue();

                        if (!CollectionUtils.isEmpty(list)) {
                            for (Object obj : list) {
                                CzfwTicketDetailShopDTO vo = new CzfwTicketDetailShopDTO();
                                BeanUtil.copyProperties(obj, vo);

                                // 经纬度
                                double[] locations = vo.splitLocation();
                                vo.setLongitude(locations[0]);
                                vo.setLatitude(locations[1]);

                                vo.setArea(e.getKey());
                                vo.setCity(ent.getKey());
                                sets.add(vo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        this.shopList = new ArrayList<>(sets);
        return this.shopList;
    }

}
