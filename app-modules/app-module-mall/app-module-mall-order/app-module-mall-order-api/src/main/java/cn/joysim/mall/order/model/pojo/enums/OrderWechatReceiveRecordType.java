package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/5
 * @desc: 微信代金券待领取记录类型
 */
public enum OrderWechatReceiveRecordType implements ValueEnum {

    /**
     * 子订单
     */
    DETAIL(0, "子订单"),
    /**
     * 发券记录
     */
    RECORD(1, "发券记录"),
    ;

    OrderWechatReceiveRecordType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderWechatReceiveRecordType fromCode(Integer code) {
        for (OrderWechatReceiveRecordType recordType : OrderWechatReceiveRecordType.values()) {
            if(recordType.getCode().equals(code)) {
                return recordType;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}


