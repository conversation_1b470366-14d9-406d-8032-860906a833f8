package cn.joysim.mall.order.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/08/25
 */
@Data
@Accessors(chain = true)
public class OrderOilQuery implements Serializable {

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 开始时间
     */
    private Date beginTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 充值产品
     */
    private Long skuId;
    /**
     * 用户Id
     */
    private Long userId;
}
