package cn.joysim.mall.order.model.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderEjyPlatformMerchandiseConfigDTO implements Serializable {

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;

    /**
     * 限制每人参与次数
     */
    private Integer limitAttendCount;

    /**
     * 优惠信息列表
     */
    private List<MerchandiseDetail> merchandiseDetails=new ArrayList<>();

    @Data
    public static class MerchandiseDetail {
        /**
         * 优惠券名字
         * 加油券
         */
        private String name;

        /**
         * 名字扩展
         * 示例值：加油券·元岗开业钜惠
         */
        private String nameExt;

        /**
         * 优惠券值
         */
        private String value;

        /**
         * 优惠券有效期
         * 示例值：2天后过期
         */
        private String limitTime;

        /**
         * 有效期扩展说明 如全天可用 12 点到 14 点可用等
         * 示例值：全天可用
         */
        private String limitTimeExplain;


        /**
         * 优惠券限制金额解释,满多少可用
         * 示例值：满10元可用
         */
        private String valueExplain;

        /**
         * 优惠券类型 1 直减券（现金券） 2 折扣券， 6 直降券
         * 示例值：1
         */
        private int merchandiseType;

        /**
         *更多规则
         * 示例值：不可叠加其他优惠
         */
        private String moreRules;

        /**
         * 优惠券类型 1 为油品 2 为商品 3 为洗车
         * 示例值：1
         */
        private int type;
    }

    public Date getStartTimeValue() {
        if (!StrUtil.isEmpty(startTime)) {
            DateTime startDateTime = DateUtil.parse(startTime);
            return startDateTime;
        }
        return null;
    }

    public Date getEndTimeValue() {
        if (!StrUtil.isEmpty(startTime)) {
            DateTime endDateTime = DateUtil.parse(endTime);
            return endDateTime;
        }
        return null;
    }
}
