package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2021/6/15
 * @desc: 小程序端卡券展示分类
 */
public enum TicketCouponTypeEnum implements ValueEnum {


    /**
     * 信用卡专属
     */
    CREDIT_CARD(0, "信用卡专属"),

    /**
     * 车主服务
     */
    CZFW(1, "车主服务"),

    /**
     * 优惠券
     */
    COUPON(2, "优惠券"),

    /**
     * 其他
     */
    OTHER(3, "其他"),

    /**
     * 全部
     */
    ALL(4,"全部"),

    /**
     * 组合礼包
     */
    GIFT_PACKAGE(5, "组合礼包"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    TicketCouponTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}
