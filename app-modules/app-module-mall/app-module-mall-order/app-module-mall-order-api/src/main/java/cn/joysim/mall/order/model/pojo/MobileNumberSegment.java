package cn.joysim.mall.order.model.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 手机号段
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_mobile_number_segment")
public class MobileNumberSegment {

    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 首号
     */
    private String prefixNumber;

    /**
     * 运营商
     */
    private String mobileOperator;

    /**
     * 省号
     */
    private String provinceNumber;

    /**
     * 区号
     */
    private String districtNumber;
}
