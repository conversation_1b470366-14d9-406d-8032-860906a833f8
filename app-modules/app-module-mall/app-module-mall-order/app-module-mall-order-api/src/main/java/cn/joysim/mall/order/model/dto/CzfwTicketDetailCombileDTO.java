package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version: 2.4.5.2
 * @author: linhs
 * @date: 2021/7/6
 * @desc: 合并车主券详细
 */
@Data
public class CzfwTicketDetailCombileDTO implements Serializable {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 权益名称
     */
    private String productName;

    /**
     * 企业Id
     */
    private Integer enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 面值
     */
    private Integer price;

    /**
     * 权益说明
     */
    private String description;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 权益类型
     */
    private Integer categoryCode;

    /**
     * 权益子类型
     */
    private Integer categorySubCode;

    /**
     * 实物礼品权益编号
     */
    private String physicalProductCode;

    /**
     * 是否显示卡券面值   0不显示，1显示
     */
    private Integer showMoney;

    private Map<String, Map<String, List<CzfwTicketDetailCombileDTO.Shop>>> shops;

    /**
     * 卡券详情列表
     */
    private List<Eticket> etickets;

    @Data
    public static class Shop implements Serializable {
        private String area;
        private String areaCode;
        private String cbfId;
        private String address;
        private String phone;
        private String name;
        private String logo;
        private String location;
        private String id;
        private Integer starLevel;
        private String beginTime;
        private String endTime;
        /**
         * 距离
         */
        private String distance;
    }

    @Data
    public static class Eticket implements Serializable {

        /**
         * 电子券ID
         */
        private Long eticketId;

        /**
         * 券码
         */
        private String code;

        /**
         * 券状态，详情见【券码状态字典】
         */
        private Integer state;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 发送时间
         */
        private String sendTime;

        /**
         * 开始生效时间
         */
        private String startTime;

        /**
         * 过期时间
         */
        private String expiredTime;

        /**
         * 总可用次数
         */
        private Integer totalTimes;

        /**
         * 已使用次数
         */
        private Integer usedTimes;

        /**
         * 是否e券商城可兑换：0-不可兑换；1-可兑换
         */
        private Integer equanExchange;

        /**
         * 预约地址
         */
        private String subscribeUrl;

        /**
         * 消费类型，1：不指定门店 0：指定
         */
        private Integer consumeType;

        /**
         * 有可消费门店id，以逗号隔开
         */
        private String shopIds;

        /**
         * 是否可评论 = 未评论 && 消费时间7天内
         * <p>
         * 1-是、0-否
         */
        private Integer canEvaluation;

        /**
         * 是否可升级：0-不可升级；1-可升级
         */
        private Integer upgradeable;

        /**
         * 可升级产品列表
         */
        private List<CzfwTicketUngradeableDTO> upGradeList;

        /**
         * 到店导航提示语
         */
        private String navigationTip;
    }

}
