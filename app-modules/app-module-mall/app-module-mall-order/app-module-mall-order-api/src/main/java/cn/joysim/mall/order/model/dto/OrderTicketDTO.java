package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 卡券订单
 */
@Data
public class OrderTicketDTO implements Serializable {

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 到账账号
     */
    private String rechargeAccount;

    /**
     * openId
     */
    private String openId;

    /**
     * appId
     */
    private String appId;
    /**
     * 卡券类型名称
     */
    private String categoryName;
    /**
     * 卡券名称
     */
    private String skuName;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 支付现金
     */
    private BigDecimal cashTotalAmount;
    /**
     * 班点支付金额
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 成本
     */
    private BigDecimal totalCost;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 备注
     */
    private String serviceRemark;
    /**
     * 备注
     */
    private String remark;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 面值
     */
    private BigDecimal amount;
    /**
     * 数量
     */
    private Integer quantity;

    private TicketTypeEnum ticketType;

    /**
     * 子订单状态
     */
    private Integer subState;

    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    /**
     * 是否银行活动订单
     * true：是
     * false：否
     */
    private Boolean bankActivityOrder;

    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 券码
     */
    private String eticketCode;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 订单来源(0:小程序,1:公众号)
     */
    private OrderSource source;

    /**
     * 到账用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rechargeUserId;

    /**
     * 是否可以作废
     */
    private Boolean toVoidAble;

    public String getCategoryName() {
        return ticketType == null ? "--" : this.ticketType.getText();
    }

}
