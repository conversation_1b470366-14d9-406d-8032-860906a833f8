package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by zhuzl on 2020/2/13.
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_shopping_cart_item")
public class ShoppingCartItem extends BaseEntity {

    /**
     * 用户Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long userId;
    /**
     * 产品SkuId
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long skuId;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 选择状态1:选中 0:未选
     */
    private Boolean selected;
    /**
     * 购买状态0:未购买 1:已购买
     */
    private Boolean state;


    /**
     * 银行兑换专区ID
     */
    private Long exchangeId;
}
