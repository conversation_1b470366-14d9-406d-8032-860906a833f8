package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderAuditDataRecordPageStatisticsDTO {



    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 电子券支付
     */
    private BigDecimal couponPayAmount;


    /**
     * 财付通支付
     */
    private BigDecimal mchPayAmount;

    /**
     * 活动金额
     */
    private BigDecimal activityAmount;

}
