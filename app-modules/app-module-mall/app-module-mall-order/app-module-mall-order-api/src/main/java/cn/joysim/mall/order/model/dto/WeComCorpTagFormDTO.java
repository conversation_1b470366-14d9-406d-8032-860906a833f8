package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.CorpTagGroupMode;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/26 17:21
 */
@Data
public class WeComCorpTagFormDTO implements Serializable {

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签所属标签组
     */
    private CorpTagGroupMode mode;

    /**
     * 标签组Id
     */
    private String groupId;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 订单类型
     */
    private OrderTypeGroupDP orderTypes;


    /**
     * 条件
     */
    private List<WeComCorpTagConditionDP> conditions;


    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;
}
