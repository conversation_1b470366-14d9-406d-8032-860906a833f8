package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/8
 * @desc: 车主服务发券后查询
 */
@Data
public class OrderCarServiceAfterSendQueryDTO implements Serializable {
    /**
     * 券记录id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long ticketRecordId;

}
