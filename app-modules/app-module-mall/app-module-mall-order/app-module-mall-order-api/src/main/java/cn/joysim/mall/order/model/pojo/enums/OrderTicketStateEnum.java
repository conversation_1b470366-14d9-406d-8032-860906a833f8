package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 电子券订单状态
 */
public enum OrderTicketStateEnum implements ValueEnum {

    /**
     * 已取消
     */
    CANCEL(-1, "已取消"),
    /**
     * 未支付
     */
    UN_PAY(0, "未支付"),
    /**
     * 待发券
     */
    UN_SEND(1, "待发券"),
    /**
     * 已发券
     */
    SEND(2, "已发券"),
    /**
     * 发送失败
     */
    FAILURE(3, "发送失败"),
    /**
     * 部分成功，部分失败
     */
    PART_SUCCESS_PART_FAILURE(4, "部分成功，部分失败"),
    /**
     * 已退款
     */
    REFUNDED(5, "已退款"),
    /**
     * 拼团中
     */
    SPELLING(6, "拼团中"),
    /**
     * 待激活
     */
    NOT_ACTIVE(7, "待激活"),
    /**
     * 已过期
     */
    EXPIRED(8, "未激活，已过期"),
    /**
     * 退款中
     */
    REFUNDING(9, "退款中"),
    /**
     * 已作废
     */
    INVALID(10, "已作废"),
    /**
     * 已使用
     */
    USED(11, "已使用"),
    /**
     * 充值中
     */
    RECHARGING(12, "充值中"),
    /**
     * 待发券(客户未登录领取),用户不存在
     */
    UN_RECEIVE(13, "待领取(客户未登录领取)"),
    /**
     * 未领取已过期
     */
    UN_RECEIVE_EXPIRE(14, "未领取已过期"),

    /**
     * 待领取（用户自行领取时，未领取）
     */
    USER_UN_RECEIVE(15, "待领取"),

    /**
     * 订单完成退款(包括中奖自动退，支付后退款)
     */
    ORDER_FINISH_REFUND(16,"订单完成退款"),

    /**
     * 人工退款
     */
    MANUAL_REFUND(17,"人工退款"),

    /**
     * 卡券过期退款
     */
    EXPIRE_REFUND(18,"卡券过期退款"),

    /**
     * 卡券过期退款中
     */
    EXPIRE_REFUNDING(19,"卡券过期退款中"),

    /**
     * 发券中
     */
    SENDING(20,"发券中"),

    /**
     * 已支付
     */
    PAYED(21, "已支付"),

    /**
     * 退款失败
     */
    REFUND_FAIL(22, "退款失败"),


    /**
     * 定时发送中
     */
    SENDING_REGULARLY(23, "定时发送中"),

    /**
     * 已完成
     */
    FINISH(24, "已完成"),

    /**
     * 资格不符
     */
    UNCONFORMITY(25,"资格不符已支付"),
    /**
     * 资格不符退款中
     */
    UNCONFORMITY_REFUNDING(26,"资格不符退款中"),

    /**
     * 资格不符退款
     */
    UNCONFORMITY_REFUND(27,"资格不符已退款"),

    /**
     * 已作废退款中
     */
    INVALID_REFUNDING(28, "已作废退款中"),

    /**
     * 预订未支付
     */
    RESERVE_UN_PAY(29,"预订未支付"),


    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderTicketStateEnum fromCode(Integer code) {
        for (OrderTicketStateEnum stateEnum : OrderTicketStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderTicketStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
