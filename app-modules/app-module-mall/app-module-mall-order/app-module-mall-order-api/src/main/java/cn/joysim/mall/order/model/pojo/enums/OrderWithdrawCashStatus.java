package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/4
 * @desc: 提现订单状态
 */
public enum OrderWithdrawCashStatus implements ValueEnum {

    /**
     * 未提交
     */
    UNTREATED(0,"未提交"),
    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 已到账
     */
    ARRIVED(2, "已到账"),

    /**
     * 提现失败
     */
    FAILED(3, "提现失败"),

    /**
     * 已退款
     */
    REFUND(4, "已退款"),

    /**
     * 已作废
     */
    CANCEL(5,"已作废"),

    /**
     * 系统转账校验中
     */
    INIT(6,"系统转账校验中"),

    /**
     * 待确认
     */
    WAIT_PAY(7,"待确认"),

    /**
     * 转账失败
     */
    FAIL(8,"转账失败"),

    /**
     * 转账成功
     */
    SUCCESS(9,"转账成功"),
    /**
     * 补发成功
     */
    //SUPPLY_AGAIN_SUCCESS(3, "补发成功"),

    /**
     * 补发失败
     */
    //SUPPLY_AGAIN_FAILD(4, "补发失败"),

    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderWithdrawCashStatus(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}


