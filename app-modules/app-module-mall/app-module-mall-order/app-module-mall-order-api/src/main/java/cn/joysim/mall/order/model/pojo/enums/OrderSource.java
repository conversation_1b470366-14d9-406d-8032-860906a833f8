package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 订单来源
 *
 * <AUTHOR>
 * @date 2021/1/11
 */
public enum OrderSource implements ValueEnum {
    /**
     * 订单来源
     */
    MINI_PROGRAM(0, "小程序"),
    OFFICIAL_ACCOUNTS(1, "公众号"),
    BOC_THIRD_PART(2, "中行第三方"),
    ABC_THIRD_PART(3, "农行第三方"),
    MINI_ALIPAY(4, "支付宝小程序"),
    H5_THIRD_PART(5, "H5第三方"),
    RCB_THIRD_PART(6, "农商行用户"),
    ADMIN_COMPENSATE(7, "管理后台补发"),
    CIB_THIRD_PART(8, "兴业第三方"),

    PICC_HENAN(9,"郑州人保")
    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderSource(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static OrderSource fromCode(Integer code) {
        for (OrderSource stateEnum : OrderSource.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }
}
