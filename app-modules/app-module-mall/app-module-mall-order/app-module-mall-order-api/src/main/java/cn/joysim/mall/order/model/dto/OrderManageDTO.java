package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/06
 */
@Data
public class OrderManageDTO implements Serializable {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 订单号
     **/
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     **/
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 下单手机
     **/
    private String mobile;
    /**
     * 用户Id
     **/
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 用户总金额
     **/
    private BigDecimal orderTotalAmount;
    /**
     * 实付金额
     **/
    private BigDecimal cashTotalAmount;
    /**
     * 订单状态码
     */
    private String state;
    /**
     * 实体订单状态码
     */
    private Integer subState;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 备注
     */
    private String serviceRemark;
    /**
     * sku名称
     */
    private String SkuName;
    /**
     * 是不是银行订单
     */
    private Boolean isBankOrder;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 班点支付金额
     */
    private BigDecimal allowanceTotalAmount;

    /**
     * esm企业账号名称
     */
    private String esmCorpName;

    /**
     * 下单时间
     */
    private Date orderTime;


    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
}
