package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingMiniActivityGroupDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderActiveState;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/4
 * 小程序订单详情
 */
@Data
public class OrderDetailMiniDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 现金支付总金额
     */
    private BigDecimal cashTotalAmount;
    /**
     * 微信优惠金额
     */
    private BigDecimal wxCouponFee;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 邮费
     */
    private Integer postage;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 签收时间
     */
    private Date signTime;
    /**
     * 发货时间
     */
    private Date sendTime;
    /**
     * 收货地址
     */
    private String receiverName;
    /**
     * 收货地址
     */
    private String receiverAddress;
    /**
     * 收货手机
     */
    private String receiverMobile;
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 物流单号
     */
    private String logisticsNum;
    /**
     * 微信支付单号
     */
    private String wxOrderNo;
    /**
     * 实物订单明细
     */
    private List<OrderEntityDetailDTO> orderDetailList;
    /**
     * 卡券明细
     */
    private List<OrderTicketDetailDTO> ticketDetail;
    /**
     * 充值明细
     */
    private OrderRechargeDetailDTO rechargeDetail;
    /**
     * 物流信息
     */
    private List<Map<String, Object>> logisticsInfo;
    /**
     * 团购信息
     */
    private MarketingMiniActivityGroupDTO groupInfo;
    /**
     * 返现津贴
     */
    private BigDecimal returnAllowance;
    /**
     * 支付津贴
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 支付类型
     */
    private OrderPaymentType paymentType;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 激活时间
     */
    private Date activeTime;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * 有效期
     */
    private Date effectiveTime;
    /**
     * 激活状态
     */
    private OrderActiveState activeState;

    /**
     * 分期总价
     **/
    private BigDecimal stagePrice;

    /**
     * 分期情况
     */
    private String stageDetail;

    /**
     * 油站名称
     */
    private String stationName;

    /**
     * 支付方式
     */
    private String paymentTypeStr;
}
