package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import cn.joysim.mall.product.exception.ProductException;
import cn.joysim.mall.product.exception.status.ProductStatusCode;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.extern.slf4j.Slf4j;

/**
 * 卡券供应商
 *
 * <AUTHOR>
 * @date 2021/03/25
 */
@Slf4j
public enum TicketSupplierEnum implements ValueEnum {

    /**
     * 景心自营
     */
    JOYSIM(0, "景心自营", TicketPlatform.ESM),
    /**
     * 滴滴代驾
     */
    DIDI(1, "滴滴代驾", TicketPlatform.ESM),
    /**
     * 广东石化
     */
    GUANG_DONG_SINOPEC(2, "广东石化", TicketPlatform.ESM),
    /**
     * 车主服务
     */
    CZFW(3, "车主服务", TicketPlatform.CZFW),
    /**
     * 微信
     */
    WECHAT(4, "微信", TicketPlatform.WECHAT),
    /**
     * 易加油
     */
    EJY(5, "易加油", TicketPlatform.EJY),
    /**
     * 班马商城
     */
    BM_MALL(6, "班马商城", TicketPlatform.BM_MALL),
    /**
     * E代驾
     */
    E_DAI_JIA(7, "E代驾", TicketPlatform.EDJ),
    /**
     * E券
     */
    E_QUAN(8, "E券", TicketPlatform.E_QUAN),

    /**
     *银行商城
     */
    E_LIFE(9, "银行商城",TicketPlatform.E_LIFE),
    /**
     *渡渡鸟
     */
    T_DUDU_BIRD(10, "渡渡鸟",TicketPlatform.T_DUDU_BIRD),
    /**
     *美团
     */
    T_MEI_TUAN(11, "美团",TicketPlatform.T_MEI_TUAN),

    /**
     * 广东中行省行
     */
    T_GD_BOC_PROVINCIAL_BANK(12, "广东中行省行", TicketPlatform.T_GD_BOC_PROVINCIAL_BANK),

    /**
     * 支付宝
     */
    ALIPAY(13, "支付宝", TicketPlatform.ALIPAY),


    /**
     * 四川中行省行
     */
    T_SC_BOC_PROVINCIAL_BANK(14,"四川中行省行",TicketPlatform.T_HN_ICBC_PROVINCIAL_BANK),

    /**
     * 湖南工行省行
     */
    T_HN_ICBC_PROVINCIAL_BANK(15,"湖南工行省行",TicketPlatform.T_HN_ICBC_PROVINCIAL_BANK),

    ;

    TicketSupplierEnum(Integer code, String text, TicketPlatform platform) {
        this.code = code;
        this.text = text;
        this.platform = platform;
    }

    @EnumValue
    private Integer code;
    private String text;
    private TicketPlatform platform;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    public TicketPlatform getTicketPlatform() {
        return this.platform;
    }

    public static TicketSupplierEnum fromName(String name) {
        for (TicketSupplierEnum supplier : TicketSupplierEnum.values()) {
            if (supplier.text.equals(name)) {
                return supplier;
            }
        }

        log.error("卡券供应商不存在，供应商：{}", name);
        throw new ProductException(ProductStatusCode.PRODUCT_SPU_SUPPLIER_EXCEPTION);
    }
}
