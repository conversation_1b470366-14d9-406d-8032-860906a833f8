package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingBankActivityDTO;
import cn.joysim.mall.marketing.model.dto.bank.MarketingBankGatherVehicleFormDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderPlatformType;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import cn.joysim.mall.user.model.pojo.MallUser;
import lombok.Data;

import java.util.List;

/**
 * 订单数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class OrderInsSaveDTO  {

    private Long paymentOrderId;

    private String remark;
    /**
     * 订单来源
     */
    private OrderSource source;

    /**
     * 用户下单请求ip
     */
    private String orderIp;

    private Long activityId;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;

    /**
     * 下单用户
     */
    private MallUser mallUser;


    private MarketingBankActivityDTO bankActivity;

    /**
     * 车险业务收集客户信息
     */
    private MarketingBankGatherVehicleFormDTO vehicleInfoDTO;

    /**
     * 下单的SKU信息
     * 这里之所以会这样处理的前提是，一个银行活动有多个SKU时，每一个SKU生成一个OrderMain
     */
    private OrderInsSKUSaveDTO orderInsSKUSaveDTO;


    private OrderTicketSaveDTO ticketOrder;

    /**
     *
     */
    private OrderInsBankDTO formData;
}
