package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/25
 */
@Data
public class OrderUpdateBaseDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;
    /**
     * 操作人
     */
    private String operatorMan;
    /**
     * 用户Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long userId;
    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否用户取消
     */
    private Boolean isUser;
}
