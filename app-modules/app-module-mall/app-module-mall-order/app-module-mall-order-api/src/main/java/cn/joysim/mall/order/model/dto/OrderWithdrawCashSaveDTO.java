package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderCashType;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderWithdrawCashStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: linhs
 * @date: 2020/6/4
 * @desc: 新增提现订单传参
 */
@Data
public class OrderWithdrawCashSaveDTO implements Serializable {

    /**
     * 订单类型
     * 0：红包
     * 1：津贴
     */
    private OrderCashType cashType;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 实际到账金额
     */
    private BigDecimal actArrivalAmount;

    /**
     * 订单提现状态
     */
    private OrderWithdrawCashStatus orderStatus;

    /**
     * 下单手机号
     */
    private String mobile;

    /**
     * 微信流水号（微信付款单号）
     */
    private String wxDetailId ;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 扩展信息
     */
    private String ext;
}
