package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.vo.MarketingMiniActivityCouponVO;
import cn.joysim.mall.order.model.vo.OrderPackageInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: linhs
 * @date: 2020/2/16
 * @desc: 结算页详情
 */
@Data
public class OrderSettleDTO implements Serializable {

    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 商品总数
     */
    private Integer totalNum;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount = BigDecimal.ZERO;
    /**
     * 使用的优惠券数量
     */
    private Integer useCouponNum;
    /**
     * 可用优惠券
     */
    private List<MarketingMiniActivityCouponVO> usableCouponList;
    /**
     * 不可用优惠券
     */
    private List<MarketingMiniActivityCouponVO> disabledCouponList;
    /**
     * 邮费
     */
    private BigDecimal postage = BigDecimal.ZERO;
    /**
     * 包裹信息
     */
    private List<OrderPackageInfoVO> packageList;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 用户津贴
     */
    private BigDecimal userAllowance;
    /**
     * 抵扣津贴
     */
    private BigDecimal useAllowance;
    /**
     * 银行活动产品价格总计
     **/
    private BigDecimal bankTotalAmount;

    /**
     * 优惠券总金额
     */
    private BigDecimal couponTotalAmount;

    /**
     * 使用银行优惠券
     */
    private List<MarketingMiniActivityCouponVO> useBankCouponList;
}
