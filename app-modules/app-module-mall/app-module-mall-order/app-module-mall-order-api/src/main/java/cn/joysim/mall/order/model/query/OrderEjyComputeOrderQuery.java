package cn.joysim.mall.order.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/14
 * @desc 计算订单请求参数
 */
@Data
@Accessors(chain = true)
public class OrderEjyComputeOrderQuery implements Serializable {

    /**
     * 易加油油站Id
     */
    @NotBlank(message = "油站Id必填")
    private Integer stationId;

    /**
     * 易加油油枪编号
     */
    @NotBlank(message = "油枪编号必填")
    private String oilgunCode;

    /**
     * 订单总金额，即用户输入金额，保留两位小数
     */
    @NotBlank(message = "用户输入金额必填")
    private String totalAmount;

    /**
     * 创建订单的手机号码
     */
    @NotBlank(message = "手机号码必填")
    private String phoneNumber;

    /**
     * 用户优惠券编号
     */
    @NotBlank(message = "用户优惠券编号必填")
    private String userCouponId;

}
