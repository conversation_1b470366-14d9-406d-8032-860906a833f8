package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @version: 2.4.16
 * @author: linhs
 * @date: 2021/12/24
 * @desc: 银联分期详情（云闪付）
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_union_stage_detail")
public class OrderUnionStageDetail extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 每期手续费
     */
    private BigDecimal poundage;

    /**
     * 分期金额=分期价/期数
     */
    private BigDecimal InstallmentAmount;



}
