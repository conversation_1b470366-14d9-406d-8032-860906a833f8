package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.5.2
 * @author: chenfukeng
 * @date: 2022/4/12
 * @desc: 易加油配置
 */
@Data
public class EJYConfigDTO implements Serializable {

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 前置密钥
     */
    private String beforeKey;

    /**
     * 后置密钥
     */
    private String afterKey;

    /**
     * 平台
     */
    private String plat;

    /**
     * 应用密钥
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * appkey加密使用
     */
    private String ak;

    /**
     * 回调签名密钥
     */
    private String sk;

    /**
     * 油站列表url
     */
    private String stationsUrl;

    /**
     * 油站详情信息url
     */
    private String stationPricesUrl;

    /**
     * 创建订单url
     */
    private String createOrderUrl;

    /**
     * 取消订单url
     */
    private String cancelOrderUrl;

    /**
     * 退款url
     */
    private String refundUrl;

    /**
     * 计算订单url
     */
    private String computeOrderUrl;

    /**
     * 用户个人优惠券查询url
     */
    private String merchandiseUrl;

    /**
     * 发券接口URL
     */
    private String platformMerchandiseV1Url;
    /**
     * 支付URL
     */
    private String paymentsUrl;

    /**
     * 查询订单
     */
    private String orderQueryUrl;

    /**
     * 商户号
     */
    //private String mchId;

    /**
     * 回调通知路由
     */
    //private String payNotifyUrl;
}
