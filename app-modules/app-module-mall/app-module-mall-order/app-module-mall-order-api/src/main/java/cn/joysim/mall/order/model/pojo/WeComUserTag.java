package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.converter.mybatis.OrderTypeGroupHandler;
import cn.joysim.mall.order.converter.mybatis.WeComCorpTagConditionHandler;
import cn.joysim.mall.order.model.dto.OrderTypeGroupDP;
import cn.joysim.mall.order.model.dto.WeComCorpTagConditionDP;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Date;
import java.util.List;

/**
 * 企微用户标签
 * <AUTHOR>
 * @date 2022/7/25 9:35
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_we_com_user_tag")
public class WeComUserTag extends BaseEntity {


    /**
     * 企业微信用户ID
     * {@link cn.joysim.mall.wecom.model.pojo.WeComGroupChatUser}
     */
//    private Long weComGroupChatUserId;


    /**
     * 企微标签Id
     * {@link WeComCorpTag}
     */
    private Long weComCorpTagId;

    /**
     * {@link cn.joysim.mall.wecom.model.pojo.MallWeComExternalContactUser}
     */
    private Long weComExternalContactUserId;
}
