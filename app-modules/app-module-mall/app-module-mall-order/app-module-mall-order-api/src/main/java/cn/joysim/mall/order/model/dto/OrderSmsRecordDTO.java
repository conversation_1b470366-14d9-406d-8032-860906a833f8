package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderSmsState;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/6/8
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderSmsRecordDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * jms发送返回的id
     */
    private String msgId;

    /**
     * 短信状态
     */
    private OrderSmsState smsState;
}
