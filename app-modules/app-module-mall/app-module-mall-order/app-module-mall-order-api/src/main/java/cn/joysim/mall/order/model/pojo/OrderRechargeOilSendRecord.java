package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 充值油卡赠送记录
 * <AUTHOR>
 * @date 2021/05/24
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_recharge_oil_send_record")
public class OrderRechargeOilSendRecord extends BaseEntity {

    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 赠送订单Id
     */
    private Long sendOrderId;
    /**
     * 赠送面值
     */
    private BigDecimal amount;
    /**
     * 赠送产品Id
     */
    private Long skuId;
    /**
     * 备注
     */
    private String remark;
}
