package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName tb_marketing_boc_e_cny_send_log
 */
@TableName(value ="tb_mall_order_ticket_biz_id_log")
@Data
public class OrderTicketBizIdLog extends BaseEntity implements Serializable {

    /**
     * 主订单Id
     */
    private Long orderId;

    /**
     * 发券记录表
     */
    private Long ticketRecordSendId;
    /**
     * 旧业务单号
     */
    private Long preBizId;

    /**
     * 新业务单号
     */
    private Long newBizId;

    /**
     * 查询上一单的发送请求
     */
    private String requestMessage;

    /**
     * 查询上一单发送结果
     */
    private String responseMessage;

}
