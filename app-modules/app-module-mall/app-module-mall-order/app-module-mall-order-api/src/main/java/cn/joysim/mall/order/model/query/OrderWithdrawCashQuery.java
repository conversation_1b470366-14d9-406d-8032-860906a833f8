package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.OrderWithdrawCashStatus;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Data
public class OrderWithdrawCashQuery {

    /**
     * 提现记录Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单提现状态
     */
    private OrderWithdrawCashStatus status;
    /**
     * 下单手机号
     */
    private String mobile;

    /**
     * 微信流水号（微信付款单号）
     */
    private String wxDetailId;
    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 开始金额
     */
    private String startAmount;

    /**
     * 结束金额
     */
    private String endAmount;

    /**
     * 用户id
     */
    private String userId;

    /**
     *企业id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long esmCorpId;


    /**
     * 商户订单号
     */
    private String partnerTradeNo;

    /**
     * 卡券订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

}
