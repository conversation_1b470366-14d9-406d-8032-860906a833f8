package cn.joysim.mall.order.model.dto;



import cn.joysim.common.utils.JsonContext;
import cn.joysim.mall.order.model.pojo.enums.CorpTagConditionType;
import cn.joysim.mall.order.model.pojo.enums.CorpTagMathSymbol;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.*;

import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/25 9:35
 */
@EqualsAndHashCode
@NoArgsConstructor
@Data
public class WeComCorpTagConditionDP {


    /**
     * 条件
     */
    private CorpTagConditionType conditionType;


    /**
     * 数学符号
     */
    private CorpTagMathSymbol symbol;


    /**
     * 金额
     */
    private BigDecimal value;


    public WeComCorpTagConditionDP(final CorpTagConditionType conditionType, final CorpTagMathSymbol symbol, final BigDecimal value) {

        Objects.requireNonNull(conditionType);
        Objects.requireNonNull(symbol);
        Objects.requireNonNull(value);

        //1）订单数量只能输入整数，金额可输入小数，数字类型不满足这两个要求时提示报错。
        if (CorpTagConditionType.ORDER_NUM.equals(conditionType)) {
            if (new BigDecimal(value.intValue()).compareTo(value)!=0) {
                throw new ValidationException("订单数量只能输入整数");
            }
        }

        this.conditionType = conditionType;
        this.symbol = symbol;
        this.value = value;
    }

}
