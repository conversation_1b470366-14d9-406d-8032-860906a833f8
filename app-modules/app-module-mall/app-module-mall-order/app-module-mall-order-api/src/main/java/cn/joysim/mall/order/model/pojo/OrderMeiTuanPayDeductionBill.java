package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/11 15:12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_mall_order_mei_tuan_pay_deduction_bill")
public class OrderMeiTuanPayDeductionBill extends BaseEntity {


    /**
     * 账单类型
     * 1、支付
     * <p>
     * 2、退款
     */
    public String type;

    /**
     * 活动ID
     **/
    public String campaignId;

    /**
     * 活动名称
     **/
    public String campaignName;

    /**
     * 支付银行
     **/
    public String bankName;

    /**
     * 支付通道
     **/
    public String payChannelName;

    /**
     * 交易成功时间
     * 账单类型=【支付】时，该字段指：支付成功时间
     * <p>
     * 账单类型=【退款】时，该字段指：退款成功时间
     */
    public Date sucTime;


    /**
     * 支付单金额
     * 账单类型=【退款】时，该字段指原支付单金额
     */
    public BigDecimal payMoney;

    /**
     * 活动优惠金额
     * 账单类型=【支付】时，该字段指：支付优惠金额
     * <p>
     * 账单类型=【退款】时，该字段指：原活动优惠金额
     */
    public BigDecimal reduceMoney;

    /**
     * 银行卡交易金额
     * 账单类型=【支付】时，该字段指：银行卡支付金额
     * <p>
     * 账单类型=【退款】时，该字段指：银行卡退款金额
     */
    public BigDecimal bankMoney;

    /**
     * 优惠实际退款金额
     * 账单类型=【支付】时，该字段赋值「-」
     */
    public BigDecimal refundMoney;

    /**
     * 卡前六后四位
     **/
    public String first6last4;


    /**
     * 请求银行支付流水号
     **/
    public String reqBankTradeNo;


    /**
     * 银行返回流水号
     **/
    public String repBankTradeNo;


    /**
     * 银行卡ID
     **/
    public String bankcardId;


    /**
     * 权益id
     */
    private String rightsId;
}
