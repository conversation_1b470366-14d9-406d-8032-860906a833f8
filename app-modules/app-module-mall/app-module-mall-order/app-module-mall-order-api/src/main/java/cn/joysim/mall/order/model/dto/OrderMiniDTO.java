package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.vo.MarketBankActivityMiniAppEcnyPacketPrizeVo;
import cn.joysim.mall.marketing.model.vo.OrderMiniWithdrawRedPacketDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/4
 */
@Data
public class OrderMiniDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 订单
     */
    private List<OrderMainMiniDTO> orderList;
    /**
     * 需要支付
     */
    private Boolean needPay;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 支付津贴
     */
    private BigDecimal allowanceTotalAmount;

    /**
     * 支付方式
     * 0：微信
     * 3：云闪付
     */
    private OrderPaymentType paymentType;

    /**
     * 银行活动Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bankActivityId;

    /**
     * 礼包详情
     */
    private String giftPackageDetail;

    /**
     * 是否是赠送订单
     */
    private Boolean includeHandSelOrder;


    @JsonUnwrapped
    private MarketBankActivityMiniAppEcnyPacketPrizeVo ecnyPacketPrizeVo;

    /**
     * 是否允许开票
     * 空：没按钮
     * true：允许开票
     * false：已开票
     */
    private Boolean allowInvoice;

    /**
     * 是否成功发放红包
     */
    private Boolean sendRedPacket;

    /**
     * 转账明细
     */
    private List<OrderMiniWithdrawRedPacketDTO> withdrawList;

}
