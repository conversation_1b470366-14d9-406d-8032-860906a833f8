package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 券补发记录
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class OrderTicketReissueRecordDTO implements Serializable {

    /**
     * 发券记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 发券记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ticketSendRecordId;
    /**
     * 订单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 补发前券产品条码
     */
    private String beforeReissueBarCode;
    /**
     * 补发后券产品条码
     */
    private String afterReissueBarCode;
    /**
     * 发券结果
     */
    private String result;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
