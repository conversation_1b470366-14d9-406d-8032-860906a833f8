package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.OrderCompensateAuditStatus;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import cn.joysim.mall.order.model.pojo.MallOrderCompensate;

import java.io.Serializable;
import java.util.Date;

/**
* {@link MallOrderCompensate}
* <AUTHOR>
* @date 2023年2月20日 下午5:04:02
*/
@Data
public class MallOrderCompensatePageQuery implements Serializable {


    /**
     * 审核状态
     */
    private OrderCompensateAuditStatus auditStatus;


    /**
     * 接券手机号
     */
    private String receiveMobile;

    /**
     * 产品名称
     */
    private String skuName;


    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动统计区名称
     */
    private String statisticsPageName;


    /**
     * 创建开始时间
     */
    private Date createTimeStart;

    /**
     * 创建结束时间
     */
    private Date createTimeEnd;

    /**
     * 审核开始时间
     */
    private Date auditTimeStart;

    /**
     * 审核结束时间
     */
    private Date auditTimeEnd;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 子项目名称
     */
    private String subProjectName;

}
