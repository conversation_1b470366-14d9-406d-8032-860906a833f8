package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.*;
import cn.joysim.mall.marketing.model.dto.MarketingActivityStatisticsPageExcelDTO;
import cn.joysim.mall.marketing.model.pojo.enums.StatisticsSettlementMode;
import cn.joysim.mall.order.model.pojo.enums.OrderCompensateAuditStatus;
import cn.joysim.mall.order.model.pojo.enums.OrderCompensateReasonType;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.bigdecimal.BigDecimalStringConverter;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import cn.joysim.mall.order.model.pojo.MallOrderCompensate;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* {@link MallOrderCompensate}
* <AUTHOR>
* @date 2023年2月20日 下午5:04:02
*/
@Data
public class MallOrderCompensatePageDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelProperty(value = "审核id",converter = LongToStringConverter.class)
    private Long id;

    /**
     * 补偿订单ID
     */
    @ExcelProperty(value = "订单id",converter = LongToStringConverter.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long compensateOrderId;


    /**
     * 订单状态
     */
    @ExcelIgnore
    private Integer orderState;


    @ExcelIgnore
    private Integer orderType;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态")
    private String orderStateText;


    @ExcelProperty(value = "用户Id",converter = LongToStringConverter.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 接券手机号
     */
    @ExcelProperty(value = "接券手机号")
    private String receiveMobile;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String skuName;

    /**
     * 产品面值
     */
    @ExcelProperty(value = "产品面值")
    private BigDecimal skuFaceValue;

    /**
     * 补偿原因
     */
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "补偿原因",converter = EnumConverter.class)
    private OrderCompensateReasonType reasonType;

    /**
     * 补偿详细说明
     */
    @ExcelProperty(value = "详细原因")
    private String reasonDetail;


    /**
     * 活动统计区名称
     */
    @ExcelProperty(value = "关联活动统计区名称")
    private String statisticsPageName;

    /**
     * 活动统计区Id
     */
    @ExcelProperty(value = "活动统计区id",converter = LongToStringConverter.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long statisticsPageId;


    /**
     * esm企业名称
     */
    @ExcelProperty(value = "esm企业")
    private String statisticsPageCorpName;


    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 提交人
     */
    @ExcelProperty(value = "提交人")
    private String creator;


    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "提交时间",converter = ConverterDateTime.class)
    private Date createTime;

    /**
     * 审核状态
     */
    @EnumConverterValueRuleAnnotation(EnumConverterValueRule.BY_TEXT)
    @ExcelProperty(value = "审核状态",converter = EnumConverter.class)
    private OrderCompensateAuditStatus auditStatus;


    /**
     * 审核人
     */
    @ExcelProperty(value = "审核人")
    private String auditUser;


    /**
     * 审核时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "审核时间",converter = ConverterDateTime.class)
    private Date auditTime;

    /**
     * 审核意见
     */
    @ExcelProperty(value = "审核意见")
    private String auditComment;

    /**
     * 券码
     */
    @ExcelProperty(value = "券码")
    private String ticketCode;

    /**
     * 主订单id
     */
    @ExcelIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderMainId;

    /**
     * 子项目名称
     */
    private String subProjectName;

    /**
     * 子项目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subProjectId;

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 项目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;

}
