package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/8
 * @desc:
 */
@Data
public class OrderExpireRefundDTO implements Serializable {
    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;
    /**
     * 券记录id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long ticketRecordId;

    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;

    /**
     * 券id
     */
    private Integer ticketId;

    /**
     * 券码
     */
    private String code;
    /**
     * 手机号
     */
    private String mobile;
}
