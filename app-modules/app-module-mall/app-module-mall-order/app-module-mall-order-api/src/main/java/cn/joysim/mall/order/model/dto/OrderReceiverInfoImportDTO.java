package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/11/7
 */
@Data
public class OrderReceiverInfoImportDTO implements Serializable {

    /**
     * 订单号
     */
    @ExcelField(title = "订单号", sort = 1)
    private Long orderId;
    /**
     * 物流公司
     */
    @ExcelField(title = "物流公司", sort = 2)
    private String logisticsCompany;
    /**
     * 物流单号
     */
    @ExcelField(title = "物流单号", sort = 3)
    private String logisticsNum;
}
