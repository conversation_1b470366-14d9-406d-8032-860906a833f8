package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderCompensateReasonType;
import cn.joysim.mall.order.model.pojo.enums.OrderReceiveTicketType;
import lombok.Data;
import cn.joysim.mall.order.model.pojo.MallOrderCompensate;

import java.io.Serializable;
import java.util.List;

/**
* {@link MallOrderCompensate}
* <AUTHOR>
* @date 2023年2月20日 下午5:04:02
*/
@Data
public class MallOrderCompensateFormDTO implements Serializable {


    /**
     * 补偿skuID
     */
    private Long skuId;

    /**
     * 活动统计区Id
     */
    private Long statisticsPageId;

    /**
     * 活动Id
     */
    private Long activityId;

    /**
     * 接券对象
     */
    private String receiveTargets;

    /**
     * 补偿原因
     */
    private OrderCompensateReasonType reasonType;

    /**
     * 补偿详细说明
     */
    private String reasonDetail;


    /**
     * 上传截图证明
     */
    private List<String> proof;

    /**
     * 短信通知
     */
    private String notifySmsContent;

    /**
     * 接券对象类型
     */
    private OrderReceiveTicketType orderReceiveTicketType;

    /**
     * 订单领取有效天数
     */
    private Integer orderReceiveDays;
}
