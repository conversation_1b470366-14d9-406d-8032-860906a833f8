package cn.joysim.mall.order.model.query;

import cn.joysim.mall.order.model.pojo.enums.TicketCouponTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketPlatform;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class EsmEticketQuery {

    /**
     * 查询状态  1未使用 2已使用 3已过期 5服务中
     */
    @NotNull(message = "订单状态不能为空")
    private Integer state;
    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 卡券类型：
     * 0：信用卡专属（斑马）
     * 1：车主服务
     * 2：优惠券
     * 3：其他
     * 4:全部
     */
    @NotNull(message = "卡券类型不能为空")
    private TicketCouponTypeEnum ticketSoure;

    /**
     * 车主服务查询状态
     */
    private String czfwState;

    private String mobile;

    /**
     * 发券平台
     */
    private List<TicketPlatform> ticketPlatforms;

}
