package cn.joysim.mall.order.model.dto;

import cn.hutool.core.util.StrUtil;
import cn.joysim.mall.order.model.pojo.enums.CorpTagGroupMode;
import com.google.common.base.Preconditions;
import lombok.Getter;

import javax.validation.ValidationException;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/25 15:03
 */
@Getter
public class WeComCorpTagGroupDP implements Serializable {
    /**
     * 标签所属标签组
     */
    private CorpTagGroupMode mode;

    /**
     * 标签组Id
     */
    private String groupId;

    /**
     * 标签组名称
     */
    private String groupName;


    public WeComCorpTagGroupDP(final CorpTagGroupMode mode, final String groupId, final String groupName) {
        Preconditions.checkNotNull(mode);
        if (CorpTagGroupMode.CHOOSE.equals(mode)) {
            if (Objects.isNull(groupId)) {
                throw new ValidationException("分组Id不能为空");
            }
        }

        if (CorpTagGroupMode.CREATE.equals(mode)) {
            if (StrUtil.isBlank(groupName)) {
                throw new ValidationException("分组名称不能为空");
            }
        }

        this.mode = mode;
        this.groupId = groupId;
        this.groupName = groupName;
    }
}
