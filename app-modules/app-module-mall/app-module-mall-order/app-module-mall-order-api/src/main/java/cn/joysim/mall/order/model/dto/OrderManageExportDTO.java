package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.common.converter.easyExcel.ChineseStringToBooleanConverter;
import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.booleanconverter.BooleanStringConverter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/06
 */
@Data
public class OrderManageExportDTO {

    /**
     * 活动id
     */
    @ExcelField(title = "活动id", sort = 1)
    @ExcelProperty(value = "活动id",converter = LongStringConverter.class)
    private Long activityId;

    /**
     * 活动名称
     */
    @ExcelField(title = "活动名称", sort = 2)
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 订单号
     **/

    @ExcelField(title = "订单号", sort = 3)
    @ExcelProperty(value = "订单号")
    private String id;
    /**
     * 支付单号
     **/
    @ExcelField(title = "支付单号", sort = 4)
    @ExcelProperty(value = "支付单号")
    private String paymentOrderId;
    /**
     * 下单手机
     **/
    @ExcelField(title = "下单手机", sort = 5)
    @ExcelProperty(value = "下单手机")
    private String mobile;
    /**
     * 下单时间
     */
    @ExcelField(title = "下单时间", sort = 6)
    @ExcelProperty(value = "下单时间",converter = ConverterDateTime.class)
    private Date orderTime;
    /**
     * 用户Id
     **/
    @ExcelField(title = "用户Id", sort = 7)
    @ExcelProperty(value = "用户Id")
    private String userId;
    /**
     * 订单总金额
     **/
    @ExcelField(title = "订单总金额", sort = 8)
    @ExcelProperty(value = "订单总金额")
    private BigDecimal orderTotalAmount;
    /**
     * 实付金额
     **/
    @ExcelField(title = "实付金额", sort = 9)
    @ExcelProperty(value = "实付金额")
    private BigDecimal cashTotalAmount;
    /**
     * 订单状态
     */
    @ExcelField(title = "订单状态", sort = 10)
    @ExcelProperty(value = "订单状态")
    private String state;
    /**
     * 订单类型
     */
    @ExcelField(title = "订单类型", sort = 11)
    @ExcelProperty(value = "订单类型")
    private String orderTypeName;
    /**
     * 备注
     */
    @ExcelField(title = "备注", sort = 12)
    @ExcelProperty(value = "备注")
    private String serviceRemark;
    /**
     * sku名称
     */
    @ExcelField(title = "sku名称", sort = 13)
    @ExcelProperty(value = "sku名称")
    private String SkuName;
    /**
     * 是不是银行订单
     */
    @ExcelField(title = "银行订单", sort = 14)
    @ExcelProperty(value = "银行订单",converter = ChineseStringToBooleanConverter.class)
    private Boolean isBankOrder;

    /**
     * 班点支付金额
     */
    @ExcelField(title = "班点支付金额", sort = 15)
    @ExcelProperty(value = "班点支付金额")
    private BigDecimal allowanceTotalAmount;

    /**
     * 使用优惠券
     */
    @ExcelField(title = "使用优惠券", sort = 16)
    @ExcelProperty(value = "使用优惠券")
    private String couponName;
    /**
     * 优惠券抵扣金额
     */
    @ExcelField(title = "优惠券抵扣金额", sort = 17)
    @ExcelProperty(value = "优惠券抵扣金额")
    private BigDecimal couponValue;

    /**
     * esm企业账号名称
     */
    @ExcelField(title = "esm企业账号名称", sort = 18)
    @ExcelProperty(value = "esm企业账号名称")
    private String esmCorpName;


}
