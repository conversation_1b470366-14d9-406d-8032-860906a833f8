package cn.joysim.mall.order.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/3/15
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderTicketBizIdLogDTO implements Serializable {

    /**
     * 主订单Id
     */
    private Long orderId;

    /**
     * 发券记录表
     */
    private Long ticketRecordSendId;
    /**
     * 旧业务单号
     */
    private Long preBizId;

    /**
     * 新业务单号
     */
    private Long newBizId;

    /**
     * 查询上一单的发送请求
     */
    private String requestMessage;

    /**
     * 查询上一单发送结果
     */
    private String responseMessage;

}
