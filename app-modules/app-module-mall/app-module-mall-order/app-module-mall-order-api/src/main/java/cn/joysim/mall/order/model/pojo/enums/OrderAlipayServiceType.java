package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;

/**
 * 支付宝订单服务枚举
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public enum OrderAlipayServiceType {


    /**
     * 充值订单服务
     */
    RECHARGE_ORDER_SERVICE(OrderTypeEnum.RECHARGE_TYPE, AlipayConstants.RECHARGE),


    ;


    private OrderTypeEnum orderType;
    private String text;

    OrderAlipayServiceType(OrderTypeEnum orderType, String text) {
        this.orderType = orderType;
        this.text = text;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public String getText() {
        return text;
    }

    public static OrderAlipayServiceType fromOrderType(OrderTypeEnum orderType) {
        for (OrderAlipayServiceType value : OrderAlipayServiceType.values()) {
            if (value.getOrderType().equals(orderType)) {
                return value;
            }
        }

        throw new OrderException(OrderStatusCode.ORDER_TYPE_ERROR);
    }

    public static class AlipayConstants {

        public static final String RECHARGE = "orderAlipayRechargeService";

    }
}
