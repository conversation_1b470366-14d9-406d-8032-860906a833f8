package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单支付数据
 *
 * <AUTHOR>
 * @date 2019/10/30
 */
@Data
public class OrderPayDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 用户openId
     */
    private String openId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * AppId
     */
    private String appId;
    /**
     * 终端ip
     */
    private String ip;
    /**
     * 回调地址
     */
    private String notifyUrl;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 支付签名
     */
    private String paySignKey;
    /**
     * 优惠标签
     */
    private String goodsTag;
    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 附加信息
     */
    private String attach;


    /*================建行支付参数==================*/
    /**
     * 商户号
     */
    private String merchantId;
    /**
     * 分行号
     */
    private String branchId;
    /**
     * 柜台号
     */
    private String posId;
    /**
     * 公钥后30位
     */
    private String pub;

    private List<OrderPayGoodsDetailDTO> goodsDetailList;

    /*==============云闪付支付参数================*/
    /**
     * 云闪付支付成功跳转回前端URL
     */
    private String ysfPaySuccessRedirectUrl;
}
