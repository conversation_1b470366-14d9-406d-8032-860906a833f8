package cn.joysim.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * http://open.ejiayou.com/docs/apis/api7#3%E3%80%81%E5%8F%91%E5%88%B8%E6%8E%A5%E5%8F%A3
 * <AUTHOR>
 * @date 2022/4/11 15:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderEjyPlatformMerchandiseV1DTO implements Serializable {

    /**
     * status领券成功
     */
    public static final Integer SUCCEED = 1;
    /**
     * status领券失败
     */
    public static final Integer FAIL = 0;

    /**
     * 1.成功 0.失败
     */
    private Integer status;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 送券时间
     */
    private String sendTime;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 优惠券活动id
     */
    private String sourceId;

    private List<Coupons> coupons;

    @Data
    public static class Coupons {
        //优惠券 id
        private Integer couponId;
        //用户优惠券编号，即优惠券唯一码
        private String userMerchandiseId;
        //券类型 1.满减券 2.折扣券
        private Integer merchandiseType;
    }

}
