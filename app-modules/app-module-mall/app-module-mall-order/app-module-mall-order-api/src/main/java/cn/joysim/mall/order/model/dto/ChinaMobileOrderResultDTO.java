package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/7/4
 * @desc: 中国移动下单结果
 */
@Data
@Accessors(chain = true)
public class ChinaMobileOrderResultDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 错误信息
     */
    private String errorMes;

    /**
     * 错误编码
     */
    private Integer errorCode;

    /**
     * 是否成功
     */
    private Boolean success;
}
