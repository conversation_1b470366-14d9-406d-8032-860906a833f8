package cn.joysim.mall.order.model.query;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 列表查询参数
 */
@Data
public class OrderQuery implements Serializable {

    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 充值账号
     */
    private String rechargeAccount;


    /**
     * 到账openId
     */
    private String openId;

    /**
     * 下单开始时间
     */
    private String startDate;
    /**
     * 下单结束时间
     */
    private String endDate;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 收货号码
     */
    private String receiverMobile;
    /**
     * 查询团购单
     */
    private Boolean isGroup;
    /**
     * 不显示送礼订单
     */
    private Boolean noShowSendGiftsOrder;
    /**
     * 充值类型
     */
    private Integer rechargeType;
    /**
     * 卡券类型
     */
    private Integer ticketType;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 订单产品类型
     * 0：非银行活
     * 1:银行活动
     * 2:全部订单
     */
    private Integer productActivityType;

    /**
     * 排序类型：
     * 0：正序
     * 1：倒序
     */
    private Integer orderByType;

    /**
     * 订单失效时间
     */
    private String expireDate;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 券码
     */
    private String eticketCode;

    /**
     * 下单手机号
     */
    private String orderMobile;

    /**
     * 网银客户号
     */
    private String unionId;

    /**
     * 核心客户号
     */
    private String cifNumber;

    /**
     * 到账用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rechargeUserId;

    /**
     * 签收开始时间
     */
    private String signStartDate;
    /**
     * 签收结束时间
     */
    private String signEndDate;

    /**
     * 银行活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bankActivityId;

    /**
     * 充值成功开始时间
     */
    private String rechargeSuccessStartDate;
    /**
     * 充值成功结束时间
     */
    private String rechargeSuccessEndDate;

}
