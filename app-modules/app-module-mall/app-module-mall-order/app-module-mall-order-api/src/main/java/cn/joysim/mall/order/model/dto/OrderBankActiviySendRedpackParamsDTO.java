package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 2.3.0
 * @author: linhs
 * @date: 2021/2/25
 * @desc: 银行活动发红包参数
 */
@Data
public class OrderBankActiviySendRedpackParamsDTO implements Serializable {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 奖品ID
     */
    private Long prizeRecordId;

    /**
     * 用户ip
     */
    private String clientIp;

    /**
     * remark.
     * 备注
     */
    private String remark;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 面额
     */
    private BigDecimal faceValue;

    /**
     * 到账手机号
     */
    private String rechargeMobile;

    /**
     * 是否跳过检查奖品
     */
    private Boolean skipCheck;
}
