package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.marketing.model.pojo.enums.MallMarketingBillTypeEnums;
import cn.joysim.mall.order.model.pojo.enums.MallOrderUniversalReportBillTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description Created with IDEA
 * @create: 2022-08-15 16:38
 * @since JDK 1.8
 */
@Data
@TableName("tb_mall_order_universal_report")
public class MallOrderUniversalReport extends BaseEntity implements Serializable {
    /**
     *活动账单管理信息id
     */
    private Long mallActivityBillId;

    /**
     * 导入账单类型
     */
    private MallMarketingBillTypeEnums importBillType;

    /**
     * 账单类型（交易类型）0:未知 1:支付 2:退款
     */
    private MallOrderUniversalReportBillTypeEnum billType;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 券ID
     */
    private String couponId;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 兑换券
     */
    private String redeemCode;

    /**
     * 优惠类型
     */
    private String discountType;

    /**
     * 面额
     */
    private BigDecimal denomination;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 交易成功时间
     */
    private Date transactionSuccessTime;

    /**
     * 发券时间
     */
    private Date sendCouponTime;

    /**
     * 消费时间（核销时间）
     */
    private Date consumerTime;

    /**
     * 支付单号（支付宝交易号）
     */
    private String paymentOrderNumber;

    /**
     * 请求银行支付的流水号
     */
    private String bankRequestSerialNumber;

    /**
     * 银行返回流水号
     */
    private String bankResponseSerialNumber;

    /**
     * 银行卡ID
     */
    private String bankId;

    /**
     * 支付银行名称
     */
    private String paymentBankName;

    /**
     * 支付银行卡卡号
     */
    private String paymentBankCardNumber;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 优惠金额（核销面额）
     */
    private BigDecimal discountAmount;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;
    /**
     * 结算发送时间
     */
    private Date settlementSendTime;

    /**
     * 结算核销时间
     */
    private Date settlementConsumerTime;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 使用开始时间
     */
    private Date useStartTime;

    /**
     * 使用结束时间
     */
    private Date useEndTime;

    /**
     * 消费区域
     */
    private String consumerArea;

    /**
     * 消费门店（门店名称、消费网点）
     */
    private String storeName;

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 检券员
     */
    private String ticketInspector;

    /**
     * 商务号（商家PID）
     */
    private String businessAccount;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 预充值方式
     */
    private String preChargeMode;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 发放渠道
     */
    private String distributionChannel;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 商圈名称
     */
    private String businessDistrictName;

    /**
     * 商圈门店名称
     */
    private String businessDistrictStoreName;

    /**
     * 消费商户号
     */
    private String consumerBusinessAccount;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 消耗门店编码(微信支付)
     */
    private String wechatConsumerStoreCode;

    /**
     * 消耗门店编码(商家自有)
     */
    private String businessConsumerStoreCode;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 部门
     */
    private String department;

    /**
     * 销售负责人
     */
    private String headOfSales;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 保单号
     */
    private String policyNumber;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 扩展json
     */
    private String extJson;

    /**
     * 归属方ID
     */
    private String ownerId;

    /**
     * 归属方名称
     */
    private String ownerName;

    /**
     * 配置方ID
     */
    private String configId;

    /**
     * 配置方名称
     */
    private String configName;

    /**
     * 退回时间
     */
    private Date refundTime;

    /**
     * esm券码
     */
    private String esmCode;

}
