package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/28
 * 购物车
 */
@Data
public class ShoppingCartDTO implements Serializable {

    /**
     * 购物车清单
     */
    private List<ShoppingCartItemDTO> shoppingCartItemList;
    /**
     * 拆分后的供应商对应的实物产品
     */
    private Map<Long, List<ShoppingCartItemDTO>> shoppingCartItemMap;
    /**
     * 优惠券
     */
    private Map<Long, List<Long>> couponIdMap;

    public ShoppingCartDTO() {
        shoppingCartItemMap = new HashMap<>();
        couponIdMap = new HashMap<>();
    }

    /**
     * 获取供应商产品
     * @param supplierId
     * @return
     */
    public List<ShoppingCartItemDTO> getShoppingCartItemBySuppler(Long supplierId) {
        return shoppingCartItemMap.computeIfAbsent(supplierId, k -> new ArrayList<>());
    }

}
