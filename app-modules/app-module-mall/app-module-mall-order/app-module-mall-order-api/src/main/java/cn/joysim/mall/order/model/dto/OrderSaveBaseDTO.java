package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingActivityCouponDiscountResultDTO;
import cn.joysim.mall.marketing.model.dto.MarketingActivityDTO;
import cn.joysim.mall.marketing.model.dto.MarketingActivitySkuDTO;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivityCouponDiscountResultDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import cn.joysim.mall.product.model.pojo.ProductSKU;
import cn.joysim.mall.product.model.pojo.ProductSupplier;
import cn.joysim.mall.user.model.pojo.MallUser;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 下单基础数据传输对象
 * <AUTHOR>
 * @date 2021/1/11
 */
@Data
public class OrderSaveBaseDTO implements Serializable {
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 支付类型
     */
    private OrderPaymentType paymentType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 使用津贴
     */
    private Boolean useAllowance;
    /**
     * 使用津贴
     */
    private Boolean onlyUseAllowance;
    /**
     * 使用优惠券
     */
    private Boolean useCoupon;
    /**
     * 送礼中心订单
     */
    private Boolean sendGiftOrder;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 银行活动二维码id
     */
    private Long marketingBankActivityQrCodeId;
    /**
     * 团购Id
     */
    private Long groupId;
    /**
     * 团购skuId
     */
    private Long groupSkuId;
    /**
     * 优惠券
     */
    private Map<Long, List<Long>> couponIdMap;
    /**
     * 汕头建行活动
     */
    private OrderShantouCCBRechargeDTO shantouCCBRecharge;
    /**
     * 订单来源
     */
    private OrderSource source;
    /**
     * 银行活动
     */
    private OrderBankDTO orderBank;
    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 客服备注
     */
    private String serviceRemark;

    //每种支付方式，可以抵扣额度
    private Map<OrderPaymentType, BigDecimal> paymentTypeAmount;

    /**===================================以上是下单参数==================================*/
    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 支付单号
     */
    private Long paymentOrderId;
    /**
     * 下单用户
     */
    private MallUser mallUser;
    /**
     * 活动信息
     */
    private MarketingActivityDTO activity;
    /**
     * 供应商
     */
    private ProductSupplier supplier;
    /**
     * 活动产品
     */
    private Map<Long, MarketingActivitySkuDTO> activitySkuCacheMap;
    /**
     * 供应商缓存
     */
    private Map<Long, ProductSupplier> supplierCacheMap;
    /**
     * 产品
     */
    private Map<Long, ProductSKU> skuCacheMap;
    /**
     * 优惠券使用明细
     */
    private MarketingActivityCouponDiscountResultDTO marketingActivityCouponDiscountResult;

    /**
     * 是否激活订单
     */
    private Boolean activeOrder;

    /**
     * 是否允许0元支付
     */
    private Boolean allowZero;

    /**
     * 银行活动优惠券结果
     */
    private MarketingBankActivityCouponDiscountResultDTO bankDiscountResult;


    public OrderSaveBaseDTO() {
        this.paymentOrderId = IdWorker.getId();
        this.sendGiftOrder = Boolean.FALSE;
        this.useAllowance = Boolean.FALSE;
        this.useCoupon = Boolean.FALSE;
        this.activeOrder = Boolean.FALSE;
        this.onlyUseAllowance = Boolean.FALSE;
        this.paymentType = OrderPaymentType.WE_CHAT;
        this.source = OrderSource.MINI_PROGRAM;

        couponIdMap = new HashMap<>();
        activitySkuCacheMap = new HashMap<>();
        supplierCacheMap = new HashMap<>();
        skuCacheMap = new HashMap<>();
    }

}
