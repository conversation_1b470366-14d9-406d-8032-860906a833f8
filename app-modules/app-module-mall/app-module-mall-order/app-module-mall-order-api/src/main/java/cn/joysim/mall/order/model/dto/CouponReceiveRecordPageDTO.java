package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.jackson.serialize.AddFieldJsonSerializer;
import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.marketing.model.vo.MarketBankActivityMiniAppEcnyPacketPrizeVo;
import cn.joysim.mall.marketing.model.vo.OrderMiniWithdrawRedPacketDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/6 15:39
 */
@Data
public class CouponReceiveRecordPageDTO {

    /**
     * sku的图片
     */
    private String imgUrl;
    /**
     * sku名称
     */
    private String skuName;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单状态
     */
    private OrderTicketStateEnum state;

    /**
     * 有效期至
     */
    private Date expiredTime;

    /**
     * 子订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderTicketDetailId;

    @JsonSerialize(using = AddFieldJsonSerializer.class )
    private OrderDetailErrorCode errorCode;

    /**
     * 展示补发按钮
     */
    private Boolean showReissueBtn;

    @JsonUnwrapped
    private MarketBankActivityMiniAppEcnyPacketPrizeVo ecnyPacketPrizeVo;

    /**
     * prizeSkuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prizeSkuId;

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPaymentId;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;

    /**
     * 是否跳转小程序
     * 默认是
     */
    private Boolean jumpMini;

    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * 是否成功发放红包
     */
    private Boolean sendRedPacket;

    /**
     * 转账明细
     */
    private List<OrderMiniWithdrawRedPacketDTO> withdrawList;
}
