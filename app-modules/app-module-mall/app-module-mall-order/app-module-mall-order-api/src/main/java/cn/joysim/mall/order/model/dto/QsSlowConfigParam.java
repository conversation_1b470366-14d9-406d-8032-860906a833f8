package cn.joysim.mall.order.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/9 14:32
 */
@Data
public class QsSlowConfigParam {

    /**
     * 商户号
     */
    @NotBlank(message = "谦硕商户号不能为空")
    private String merchantId;

    /**
     * 回调地址
     */
    @NotBlank(message = "谦硕充值回调URL不能为空")
    private String notifyUrl;


    /**
     * md5
     */
    @NotBlank(message = "谦硕充值Md5值不能为空")
    private String md5;

    /**
     * 超时时间
     */
    @NotNull(message = "谦硕充值超时时间不能为空")
    private Integer timeout;

}
