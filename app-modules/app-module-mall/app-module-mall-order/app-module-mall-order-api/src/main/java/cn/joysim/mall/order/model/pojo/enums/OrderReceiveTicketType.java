package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 2.7.19
 * @author: linhs
 * @date: 2023/3/27
 * @desc: 接券对象
 */
public enum OrderReceiveTicketType implements ValueEnum {

    /**
     * 接券手机号
     */
    MOBILE (0, "接券手机号"),
    /**
     * 用户ID
     */
    USER_ID(1, "用户ID"),


    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderReceiveTicketType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}

