package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/07/06
 * 电子券订单状态
 */
public enum OrderBocStateEnum implements ValueEnum {


    /**
     * 未处理
     */
    UN_DEAL(0, "未处理"),
    /**
     * 支付
     */
    PAYED(1, "支付"),
    /**
     * 撤销
     */
    REVOKE(2, "撤销"),
    /**
     * 退货
     */
    REFUNDED(3, "退货"),
    /**
     * 未明
     */
    NOT_CLEAR(4, "未明"),
    /**
     * 失败
     */
    FAIL(5, "失败"),
    /**
     * 下单
     */
    ORDERING(6, "下单"),


    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderBocStateEnum fromCode(Integer code) {
        for (OrderBocStateEnum stateEnum : OrderBocStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderBocStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
