package cn.joysim.mall.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/21 14:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCountTyServiceFeeDTO {

    private BigDecimal targetServiceFee;

    private BigDecimal targetAmount;

}
