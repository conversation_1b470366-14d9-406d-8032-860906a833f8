package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 微信资金账单记录表
 * @TableName tb_mall_order_wx_fund_flow
 */
@TableName(value ="tb_mall_order_wx_fund_flow")
@Data
public class OrderWxFundFlow extends BaseEntity implements Serializable {

    /**
     * 记账时间
     */
    private Date amountTime;

    /**
     * 微信支付业务单号
     */
    private String wxPaymentNum;

    /**
     * 资金流水单号
     */
    private String capitalFlowNum;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 收支类型
     */
    private String paymentType;

    /**
     * 收支金额
     */
    private BigDecimal amount;

    /**
     * 账户结余（元）
     */
    private BigDecimal balance;

    /**
     * 资金变更申请人
     */
    private String amountChangeApplicant;

    /**
     * 备注
     */
    private String note;

    /**
     * 业务凭证
     */
    private String businessVoucher;


    /**
     * 商户号
     */
    private String mchId;


    /**
     * 应用Id
     */
    private String appId;


    /**
     * 账户类型
     */
    private String accountType;

}