package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2021/5/26
 * @desc: 车主服务卡券详情
 */
@Data
public class CzfwTicketDetailResultDTO implements Serializable {

    /**
     * 电子券权益id
     */
    private Integer eticketProductId;
    /**
     * 企业Id
     */
    private Integer enterpriseId;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 权益名称
     */
    private String productName;
    /**
     * 权益说明
     */
    private String description;

    /**
     * 使用说明
     */
    private String instructions;


    /**
     * 卡券列表
     */
    private List<TicketDetail> ticketDetailList;

    /**
     * 券列表
     */
    private List<CzfwTicketDetailCombileDTO.Eticket> etickets;

    /**
     * 门店
     */
    private List<CzfwTicketDetailCombileDTO.Shop> ticketShopList;

    /**
     * 城市对应门店
     */
    private Map<String, Map<String, List<CzfwTicketDetailCombileDTO.Shop>>> shops;

    @Data
    public static class TicketDetail implements Serializable {
        /**
         * 电子券Id
         */
        private Integer id;
        /**
         * 面值
         */
        private Integer price;
        /**
         * 开始生效时间
         */
        private String startTime;
        /**
         * 过期时间
         */
        private String expiredTime;
        /**
         * 发送时间
         */
        private String sendTime;
        /**
         * 消费类型，1：不指定门店 0：指定
         */
        private Integer consumeType;
        /**
         * 券码
         */
        private String code;
        /**
         * 手机号
         */
        private String mobile;
        /**
         * 总可用次数
         */
        private Integer totalTimes;
        /**
         * 已使用次数
         */
        private Integer usedTimes;
        /**
         * 有可消费门店id，以逗号隔开
         */
        private String shopIds;
        /**
         * 券状态，详情见【券码状态字典】
         */
        private Integer state;
        /**
         * 权益类型
         */
        private Integer categoryCode;
        /**
         * 权益类型名称
         */
        private String category;
        /**
         * 权益子类型
         */
        private Integer categorySubCode;
        /**
         * 权益子类型名称
         */
        private String categorySub;
        /**
         * 实物礼品权益编号
         */
        private String physicalProductCode;
        /**
         * 是否可评论 = 未评论 && 消费时间7天内
         * <p>
         * 1-是、0-否
         */
        private Integer canEvaluation;
        /**
         * 是否显示卡券面值   0不显示，1显示
         */
        private Integer showMoney;
        /**
         * 消费门店名称Id
         */
        private Integer validateShopId;
        /**
         * 消费门店名称
         */
        private String validateShop;
        /**
         * 消费时间
         */
        private String validateTime;

        /**
         * 可升级产品列表
         */
        private List<TicketDetail> upGradeList;

        /**
         * 预约地址
         */
        private String subscribeUrl;

    }



}
