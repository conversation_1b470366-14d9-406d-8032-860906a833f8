package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2020/6/5
 * @desc: 订单短信状态
 */
public enum OrderSmsState implements ValueEnum {

    /**
     * 无
     */
    NOTHING(-1, "无"),
    /**
     * 发送中
     */
    SENDING(0, "发送中"),
    /**
     * 发送成功
     */
    SEND_SUCCESS(1, "发送成功"),
    /**
     * 发送失败
     */
    SEND_FAIL(2, "发送失败"),
    /**
     * 待发送
     */
    TO_BE_SEND(3, "待发送"),
    ;

    OrderSmsState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderTypeEnum fromCode(Integer code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

}


