package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 主订单分组信息记录
 * @TableName tb_mall_order_main_group
 */
@TableName(value ="tb_mall_order_main_group")
@Data
public class OrderMainGroup  implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

}