package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.mall.order.exception.OrderException;
import cn.joysim.mall.order.exception.status.OrderStatusCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 充值服务类
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
@Slf4j
public enum RechargeServiceType {

    /**
     * 话费快充
     */
    FAST_RECHARGE(RechargeTypeEnum.FAST_RECHARGE, RechargeSupplierEnum.SUPPLIER_OFPAY, Constants.FAST_RECHARGE),
    /**
     * 娱乐充值
     */
    MEDIA_RECHARGE(RechargeTypeEnum.MEDIA_RECHARGE, RechargeSupplierEnum.SUPPLIER_OFPAY, Constants.MEDIA_RECHARGE),
    /**
     * 流量充值
     */
    FLOW_RECHARGE(RechargeTypeEnum.MOBILE_TRAFFIC_RECHARGE, RechargeSupplierEnum.SUPPLIER_OFPAY, Constants.FLOW_RECHARGE),
    /**
     * 生活充值
     */
    LIFE_RECHARGE(RechargeTypeEnum.LIFE_RECHARGE, RechargeSupplierEnum.SUPPLIER_WH, Constants.LIFE_RECHARGE),
    /**
     * 话费慢充(分销)
     */
    SLOW_RECHARGE_FXPT(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_FXPT, Constants.SLOW_RECHARGE_FXPT),
    /**
     * 话费慢充(正联)
     */
    SLOW_RECHARGE_ZL(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_ZL, Constants.SLOW_RECHARGE_ZL),
    /**
     * 话费慢充(方知)
     */
    SLOW_RECHARGE_FZ(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_FZ, Constants.SLOW_RECHARGE_FZ),
    /**
     * 话费慢充(乐语)
     */
    SLOW_RECHARGE_LY(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_LY, Constants.SLOW_RECHARGE_LY),
    /**
     * 话费快充(万恒)
     */
    FAST_RECHARGE_WH(RechargeTypeEnum.FAST_RECHARGE, RechargeSupplierEnum.SUPPLIER_WH, Constants.FARE_RECHARGE_WH),
    /**
     * 话费慢充(华高)
     */
    SLOW_RECHARGE_HG(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_HG, Constants.SLOW_RECHARGE_HG),

    /**
     * 有钱途
     */
    FAST_RECHARGE_YQT(RechargeTypeEnum.FAST_RECHARGE, RechargeSupplierEnum.SUPPLIER_YQT, Constants.FAST_RECHARGE_YQT),

    /**
     * 话费慢充韬铭
     */
    SLOW_RECHARGE_TM(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_TM, Constants.SLOW_RECHARGE_TM),
    /**
     * E券石化油卡充值
     */
    E_QUAN_SINOPEC_OIL_CARD(RechargeTypeEnum.R_SINOPEC_OIL_CARD, RechargeSupplierEnum.E_QUAN, Constants.E_QUAN_OIL_CARD),
    /**
     * E券中石油卡充值
     */
    E_QUAN_CNPC_OIL_CARD(RechargeTypeEnum.R_CNPC_OIL_CARD, RechargeSupplierEnum.E_QUAN, Constants.E_QUAN_OIL_CARD),

    /**
     * 谦硕慢充
     */
    SLOW_RECHARGE_QS(RechargeTypeEnum.SLOW_RECHARGE, RechargeSupplierEnum.SUPPLIER_QS,Constants.SLOW_RECHARGE_QS),

    /**
     * 卡池快充
     */
    FAST_RECHARGE_KA_CHI(RechargeTypeEnum.FAST_RECHARGE, RechargeSupplierEnum.KA_CHI,Constants.FAST_RECHARGE_KA_CHI),

    /**
     * 娱乐充值
     */
    MEDIA_RECHARGE_WH(RechargeTypeEnum.MEDIA_RECHARGE, RechargeSupplierEnum.SUPPLIER_WH, Constants.MEDIA_RECHARGE_WH),

    /**
     * 充值中心中石化油卡充值
     */
    RC_CENTER_SINOPEC_OIL_CARD(RechargeTypeEnum.R_SINOPEC_OIL_CARD, RechargeSupplierEnum.RECHARGE_CENTER, Constants.RC_CENTER_OIL_CARD),


    /**
     * 充值中心中石油油卡充值
     */
    RC_CENTER_CNPC_OIL_CARD(RechargeTypeEnum.R_CNPC_OIL_CARD, RechargeSupplierEnum.RECHARGE_CENTER, Constants.RC_CENTER_OIL_CARD),

    /**
     * 充值中心话费充值
     */
    RC_CENTER_FAST_RECHARGE(RechargeTypeEnum.FAST_RECHARGE, RechargeSupplierEnum.RECHARGE_CENTER, Constants.RC_CENTER_OIL_CARD),

    ;

    private RechargeTypeEnum rechargeType;
    private RechargeSupplierEnum supplier;
    private String text;

    RechargeServiceType(RechargeTypeEnum rechargeType, RechargeSupplierEnum supplier, String text) {
        this.rechargeType = rechargeType;
        this.supplier = supplier;
        this.text = text;
    }

    public RechargeTypeEnum getRechargeType() {
        return rechargeType;
    }

    public RechargeSupplierEnum getSupplier() {
        return supplier;
    }

    public String getText() {
        return text;
    }

    public static RechargeServiceType fromRechargeTypeAndSupplier(RechargeTypeEnum rechargeType, RechargeSupplierEnum supplier) {
        for (RechargeServiceType value : RechargeServiceType.values()) {
            if (value.getRechargeType().equals(rechargeType) && value.getSupplier().equals(supplier)) {
                return value;
            }
        }

        log.info("获取充值服务类错误，充值类型={}，充值供应商={}", rechargeType, supplier);
        throw new OrderException(OrderStatusCode.ORDER_RECHARGE_TYPE_ERROR);
    }

    public static class Constants {
        public static final String FAST_RECHARGE = "fastRechargeService";
        public static final String SLOW_RECHARGE_FXPT = "fxptSlowRechargeService";
        public static final String SLOW_RECHARGE_ZL = "zlSlowRechargeService";
        public static final String SLOW_RECHARGE_FZ = "fzSlowRechargeService";
        public static final String SLOW_RECHARGE_LY = "lySlowRechargeService";
        public static final String MEDIA_RECHARGE = "mediaRechargeService";
        public static final String FLOW_RECHARGE = "flowRechargeService";
        public static final String LIFE_RECHARGE = "lifeRechargeService";
        public static final String FARE_RECHARGE_WH = "whFareRechargeService";
        public static final String SLOW_RECHARGE_HG = "hgSlowRechargeService";
        public static final String FAST_RECHARGE_YQT = "yqtfastRechargeService";
        public static final String SLOW_RECHARGE_TM = "tmSlowRechargeService";
        public static final String E_QUAN_OIL_CARD = "eQuanOilCardRechargeService";
        public static final String SLOW_RECHARGE_QS = "qsSlowRechargeService";
        public static final String FAST_RECHARGE_KA_CHI = "kaChiRechargeService";

        public static final String MEDIA_RECHARGE_WH = "whMediaRechargeService";
        public static final String RC_CENTER_OIL_CARD = "rcCenterOilCardRechargeService";
    }

}
