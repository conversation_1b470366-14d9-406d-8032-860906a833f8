package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.converter.jackson.OrderTypeGroupSerialize;
import cn.joysim.mall.order.converter.jackson.WeComCorpTagConditionSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/25 16:18
 */
@Data
public class WeComCorpTagGroupDTO implements Serializable {


    /**
     * 分组Id
     */
    private String groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 排序
     */
    private Integer order;

    public WeComCorpTagGroupDTO(String groupId, String groupName, Integer order) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.order = order;
    }
}
