package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.BankActivityMode;
import cn.joysim.mall.order.model.pojo.enums.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发券记录DTO
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
public class OrderTicketSendRecordDTO implements Serializable {

    /**
     * 发券记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ticketRecordId;

    /**
     * 活动信息-ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动信息-名称
     */
    private String activityName;

    /**
     * 活动信息-活动模式
     */
    private BankActivityMode activityMode;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 订单状态
     */
    private OrderTicketStateEnum orderStatus;

    /**
     * 券状态
     */
    private Integer ticketStatus;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * openId
     */
    private String openId;

    /**
     * 券产品信息-产品ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    /**
     * 券产品信息-产品名称
     */
    private String skuName;

    /**
     * 组合礼包id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long composeGiftId;

    /**
     * 组合礼包名称
     */
    private String composeGiftName;

    /**
     * 券产品条码
     */
    private String barCode;

    /**
     * 记录充值编码
     */
    private String chargeCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 卡券类型
     */
    private TicketTypeEnum ticketType;

    /**
     * 订单券类型
     */
    private TicketTypeEnum orderTicketType;

    /**
     * 面额
     */
    private BigDecimal faceValue;

    /**
     * 券记录状态
     */
    private TicketStateEnum ticketRecordState;

    /**
     * 发券时间
     */
    private Date sendTicketTime;

    /**
     * 预发券时间
     */
    private Date preSendTicketTime;

    /**
     * 券码
     */
    private String ticketCode;

    /**
     * 消费时间
     */
    private Date consumeTime;

    /**
     * 到期时间
     */
    private Date expiredTime;

    /**
     * 发券结果信息
     */
    private String sendTicketResult;

    /**
     * 网点二维码ID
     */
    private String qrCodeId;

    /**
     * 发券人手机号
     */
    private String sendManMobile;

    /**
     * 下单手机号
     */
    private String activityRecordMobile;

    /**
     * 第三方券码
     */
    private String thirdCouponId;

    /**
     * 卡券平台
     */
    private Integer platform;

    /**
     * 充供应商
     */
    private TicketSupplierEnum ticketSupplier;


    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;
}
