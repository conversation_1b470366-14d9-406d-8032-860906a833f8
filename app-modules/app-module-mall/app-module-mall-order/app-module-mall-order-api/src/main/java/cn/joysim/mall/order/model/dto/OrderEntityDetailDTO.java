package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.product.model.dto.ProductSKUImgDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 实物订单明细
 */
@Data
public class OrderEntityDetailDTO implements Serializable {

    /**
     * 订单明细id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 产品skuId
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long skuId;
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 购买价格
     */
    private BigDecimal price;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 优惠明细
     */
    private List<OrderDiscountDetailDTO> orderDiscountDetailList;
    /**
     * 产品图
     */
    private List<ProductSKUImgDTO> skuImgList;
}
