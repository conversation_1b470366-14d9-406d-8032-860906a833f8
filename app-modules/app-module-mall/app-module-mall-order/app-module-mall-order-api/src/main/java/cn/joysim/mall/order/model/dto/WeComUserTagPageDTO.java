package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/7/26 17:28
 */
@Data
public class WeComUserTagPageDTO implements Serializable {


    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * openId
     */
    private String openId;

    /**
     * unionId
     */
    private String unionId;


    /**
     * 用户昵称
     */
    private String nickname;


    /**
     * 手机号
     */
    private String mobile;


    /**
     * 客户联系人
     */
    private String externalUserId;

}
