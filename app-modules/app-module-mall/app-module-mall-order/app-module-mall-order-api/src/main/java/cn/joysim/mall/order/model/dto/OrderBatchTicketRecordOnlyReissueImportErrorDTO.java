package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/1/7
 */
@Data
@Accessors(chain = true)
public class OrderBatchTicketRecordOnlyReissueImportErrorDTO implements Serializable {


    /**
     * 发券记录ID
     */
    @ExcelProperty(value = "发券记录ID")
    private String sendRecordId;


    @ExcelProperty(value = "导入结果")
    private String errorMessage;



}
