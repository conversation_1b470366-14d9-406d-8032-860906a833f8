package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2020/8/15
 * @desc: 任务实物订单
 */
@Data
public class OrderMissionEntityDTO implements Serializable {

    /**
     * 下单用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 邀请人用户Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long invitorUserId;

}
