package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/16
 * @desc: 订单赠送状态
 */
public enum OrderSendState implements ValueEnum {
    UN_DEAL(0, "未赠送"),
    DEALING(1, "处理中"),
    SENDED(2, "已赠送"),
    FAIL(3, "赠送失败"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderSendState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static OrderSendState fromCode(Integer code) {
        for (OrderSendState stateEnum : OrderSendState.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }
}
