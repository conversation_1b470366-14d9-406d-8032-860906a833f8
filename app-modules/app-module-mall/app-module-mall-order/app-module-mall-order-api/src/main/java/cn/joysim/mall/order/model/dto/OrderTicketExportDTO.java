package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.mall.order.model.pojo.OrderPaymentDocument;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/25
 * 充值订单导出
 */
@Data
public class OrderTicketExportDTO implements Serializable {


    /**
     * 订单号
     */
    @ExcelField(title = "订单号")
    @ExcelProperty(value = "订单号")
    private String id;

    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID")
    @ExcelProperty(value = "用户ID")
    private String userId;
    /**
     * 下单时间
     */
    @ExcelField(title = "下单时间")
    @ExcelProperty(value = "下单时间")
    private String orderTime;
    /**
     * 发送时间
     */
    @ExcelField(title = "发送时间")
    @ExcelProperty(value = "发送时间")
    private String sendTime;
    /**
     * 卡券类型
     */
    @ExcelField(title = "卡券类型")
    @ExcelProperty(value = "卡券类型")
    private String categoryName;
    /**
     * 卡券名称
     */
    @ExcelField(title = "卡券名称")
    @ExcelProperty(value = "卡券名称")
    private String skuFinalName;
    /**
     * 订单金额
     */
    @ExcelField(title = "订单金额")
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderTotalAmount;
    /**
     * 实付金额
     */
    @ExcelField(title = "实付金额")
    @ExcelProperty(value = "实付金额")
    private BigDecimal cashTotalAmount;
    /**
     * 班点支付金额
     */
    @ExcelField(title = "班点支付金额")
    @ExcelProperty(value = "班点支付金额")
    private BigDecimal allowanceTotalAmount;
    /**
     * 成本
     */
    @ExcelField(title = "成本")
    @ExcelProperty(value = "成本")
    private BigDecimal totalCost;
    /**
     * 下单手机号
     */
    @ExcelField(title = "下单手机号")
    @ExcelProperty(value = "下单手机号")
    private String mobile;
    /**
     * 到账账号
     */
    @ExcelField(title = "到账账号")
    @ExcelProperty(value = "到账账号")
    private String rechargeAccount;

    /**
     * 到账用户ID
     */
    @ExcelField(title = "到账用户ID")
    @ExcelProperty(value = "到账用户ID")
    private String rechargeUserId;

    /**
     * 到账openID
     */
    @ExcelField(title = "到账openId")
    @ExcelProperty(value = "到账openId")
    private String openId;

    /**
     * 订单状态
     */
    @ExcelField(title = "订单状态")
    @ExcelProperty(value = "订单状态")
    private String stateStr;
    /**
     * 面额
     */
    @ExcelField(title = "面额")
    @ExcelProperty(value = "面额")
    private BigDecimal amount;
    /**
     * 数量
     */
    @ExcelField(title = "数量")
    @ExcelProperty(value = "数量")
    private Integer quantity;
    /**
     * 备注
     */
    @ExcelField(title = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 获得总津贴
     */
    @ExcelField(title = "获得津贴")
    @ExcelProperty(value = "获得津贴")
    private BigDecimal obtainTotalAllowance;
    /**
     * 微信优惠券
     */
    @ExcelField(title = "微信优惠券")
    @ExcelProperty(value = "微信优惠券")
    private String wxCoupon;
    /**
     * 微信优惠金额
     */
    @ExcelField(title = "微信优惠金额")
    @ExcelProperty(value = "微信优惠金额")
    private BigDecimal wxDiscountAmount;
    /**
     * 备注
     */
    @ExcelField(title = "客服备注")
    @ExcelProperty(value = "客服备注")
    private String serviceRemark;

    /**
     * 是否需要激活
     */
    @ExcelField(title = "是否需要激活")
    @ExcelProperty(value = "是否需要激活")
    private String isActivate;

    /**
     * 激活时间
     */
    @ExcelField(title = "激活时间")
    @ExcelProperty(value = "激活时间")
    private String activeTime;
    /**
     * 支付单号
     */
    @ExcelField(title = "支付单号")
    @ExcelProperty(value = "支付单号")
    private String paymentOrderId;

    /**
     *
     * 银联流水号
     **/
    @ExcelField(title = "银联流水号")
    @ExcelProperty(value = "银联流水号")
    private String  unionOrderId;

    /**
     * 景心优惠
     */
    @ExcelField(title = "景心优惠")
    @ExcelProperty(value = "景心优惠")
    private String joysimPreferential;
    /**
     * 商户贴息
     */
    @ExcelField(title = "商户贴息")
    @ExcelProperty(value = "商户贴息")
    private String joysimAccrual;
    /**
     * 分期情况
     */
    @ExcelField(title = "分期情况")
    @ExcelProperty(value = "分期情况")
    private String stageDetail;
    /**
     * 银行卡号
     */
    @ExcelField(title = "银行卡号")
    @ExcelProperty(value = "银行卡号")
    private String accNo;

    /**
     * 活动id
     */
    @ExcelField(title = "活动id")
    @ExcelProperty(value = "活动id")
    private String activityId;

    /**
     * 活动名称
     */
    @ExcelField(title = "活动名称")
    @ExcelProperty(value = "活动名称")
    private String activityName;

    @ExcelField(title ="网点手机号")
    @ExcelProperty(value = "网点手机号")
    private  String customerPhone;

    /**
     * 优惠券
     */
    @ExcelField(title = "优惠券")
    @ExcelProperty(value = "优惠券")
    private String couponInfo;

    /**
     * sku条码
     */
    @ExcelField(title = "sku条码")
    @ExcelProperty(value = "sku条码")
    private String barCode;

    /**
     * sku券码
     */
    @ExcelField(title = "sku券码")
    @ExcelProperty(value = "sku券码")
    private String code;

    /**
     * 订单状态
     */
    @ExcelIgnore
    private Integer state;

    /**
     * 电子券类型
     */
    @ExcelIgnore
    private Integer ticketType;

    @ExcelIgnore
    private OrderPaymentDocument paymentDocument;

    /**
     * 微信优惠券Id
     */
    @ExcelIgnore
    private String wxCouponId;


    /**
     * sku卡券状态
     */
    @ExcelField(title = "sku卡券状态")
    @ExcelProperty(value = "sku卡券状态")
    private String ticketState;


    @ExcelField(title = "sku卡券核销时间")
    @ExcelProperty(value = "sku卡券核销时间")
    private String wxUseTime;

    /**
     * 第三方活动id
     */
    @ExcelField(title = "第三方活动id")
    @ExcelProperty(value = "第三方活动id")
    private String thirdBatchId;

    /**
     * 第三方券码
     */
    @ExcelField(title = "第三方券码")
    @ExcelProperty(value = "第三方券码")
    private String thirdCouponId;

    /**
     * 优惠总金额
     */
    @ExcelIgnore
    private BigDecimal discountTotalAmount;

}
