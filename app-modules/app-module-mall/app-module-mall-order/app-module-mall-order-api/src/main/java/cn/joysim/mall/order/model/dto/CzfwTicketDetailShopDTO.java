package cn.joysim.mall.order.model.dto;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Data
public class CzfwTicketDetailShopDTO {

    private Integer serviceType;
    private String province;
    private String city;
    private String area;
    private String address;
    private String areaCode;
    private Integer type;
    private String serviceTypeText;
    private String name;
    private String logo;
    private String location;
    private Integer id;
    private Integer starLevel;
    private String beginTime;
    private String endTime;
    private String state;
    private Integer cbfId;
    private String phone;

    /**
     * 經度
     */
    private Double longitude;

    /**
     * 緯度
     */
    private Double latitude;

    /**
     * 两个经纬度之间的直线距离
     */
    private Double distance;

    /**
     * 完整地址
     */
    private String fullAddress;

    public double[] splitLocation() {
        double[] result = {0.0, 0.0};
        if (StringUtils.isEmpty(this.location) || !this.location.contains(",")) {
            return result;
        }

        try {
            String[] ary = this.location.split(",");
            result[0] = Double.parseDouble(ary[0]);
            result[1] = Double.parseDouble(ary[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
