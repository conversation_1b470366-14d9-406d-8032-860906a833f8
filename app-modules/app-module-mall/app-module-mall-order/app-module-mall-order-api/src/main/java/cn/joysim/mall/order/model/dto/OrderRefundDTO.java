package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/11/10
 */
@Data
public class OrderRefundDTO implements Serializable {

    /**
     * 小程序appId
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 订单金额
     */
    private BigDecimal totalAmount;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款回调
     */
    private String notifyUrl;
    /**
     * 微信订单号
     */
    private String wxOrderNo;
    /**
     * 商户订单号
     */
    private Long orderId;
    /**
     * 商户退款单号
     */
    private Long refundId;
    /**
     * 支付单号
     */
    private Long paymentOrderId;
    /**
     * 证书路径
     */
    private String certPath;
    /**
     * 退款原因
     */
    private String refundDesc;
    /**
     * 支付签名Key
     */
    private String paySignKey;
}
