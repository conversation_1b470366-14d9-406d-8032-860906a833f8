package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/10
 * 油站价格
 */
@Data
public class OrderEjyStationPriceDTO implements Serializable {

    /**
     * 油号 id
     */
    String oilId;

    /**
     * 油号
     */
    String oilCode;

    /**
     * 油类型，1.汽油 2.柴油
     */
    int oilType;

    /**
     * 油站挂牌价
     */
    String stationPrice;

    /**
     * 优惠后单价
     */
    String discountPrice;

    /**
     * 国家价
     */
    String countryPrice;

    /**
     * 油枪号列表
     */
    List<String> oilgunCodes;

}
