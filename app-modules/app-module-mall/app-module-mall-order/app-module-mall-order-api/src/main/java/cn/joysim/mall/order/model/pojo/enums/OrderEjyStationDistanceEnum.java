package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/03/15
 * 易加油油站距离范围
 */
public enum OrderEjyStationDistanceEnum implements ValueEnum {

    /**
     * 6km内
     */
    DIS_6km(0, "6km"),
    /**
     * 10km内
     */
    DIS_10km(1, "10km"),
    /**
     * 15km内
     */
    DIS_15km(2, "15km"),
    /**
     * 20km内
     */
    DIS_20km(3, "20km"),
    /**
     * 50km内
     */
    DIS_50km(4, "50km"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderEjyStationDistanceEnum fromCode(Integer code) {
        for (OrderEjyStationDistanceEnum stateEnum : OrderEjyStationDistanceEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderEjyStationDistanceEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public Integer getDistance(){
        switch (code){
            case 0:
                return 6;
            case 1:
                return 10;
            case 2:
                return 15;
            case 3:
                return 20;
            case 4:
                return 50;
            default:
                return 50;
        }
    }
}
