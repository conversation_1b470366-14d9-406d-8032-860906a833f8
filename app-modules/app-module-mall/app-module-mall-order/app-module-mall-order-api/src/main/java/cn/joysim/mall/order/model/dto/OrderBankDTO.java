package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.enums.CustomBusinessCheckAppendix;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivityCalculateResultDTO;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivityConfigDTO;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivityDTO;
import cn.joysim.mall.marketing.model.dto.MarketingBankActivitySkuDTO;
import cn.joysim.mall.marketing.model.dto.bank.MarketingBankCustomBusinessDetailItemSaveDTO;
import cn.joysim.mall.marketing.model.dto.bank.MarketingBankGatherInfoSaveDTO;
import cn.joysim.mall.marketing.model.dto.bank.MarketingBankGatherVehicleFormDTO;
import cn.joysim.mall.marketing.model.pojo.MarketingBankActivityPrizeSku;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityCardGradeType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityOrderType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingPrizeGrantType;
import cn.joysim.mall.marketing.model.vo.MarketingBankActivityCustomBusinessMetaFormVO;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/07/23
 */
@Data
public class OrderBankDTO implements Serializable {

    /**
     * 活动Id
     */
    private Long activityId;
    /**
     * 银行卡
     */
    private String bankCard;
    /**
     * 充值产品
     */
    private Long skuId;
    /**
     * 充值产品
     */
    private Long bankSkuId;
    /**
     * 手机号
     */
    private String validMobile;
    /**
     * 充值账号
     */
    private String rechargeMobile;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 验证码
     */
    private String code;
    /**
     * 订单来源
     */
    private OrderSource source;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 限定分期期数
     */
    private Integer limitNum;

    /**
     * 支付方式
     * 0：微信
     * 3：云闪付
     */
    private OrderPaymentType paymentType;

    /**
     * 银行Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bankId;

    /**
     * 收货地址
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long addressId;

    /**
     * 活动产品
     */
    private MarketingBankActivitySkuDTO activitySku;

    /**
     * 景心优惠
     */
    private String joysimPreferential;

    /**
     * wwq
     * 商户是否贴息
     **/
    private Boolean isDiscount;


    /**
     * 持卡人等级
     */
    private MarketingBankActivityCardGradeType cardGradeType;

    /**
     * 缓存
     * 用户位置
     */
    private String userLocation;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 客户号
     */
    private String unionId;

    /**
     * 是否抽奖订单
     */
    private Boolean lotteryDraw;

    /**
     * 配置信息
     */
    private MarketingBankActivityConfigDTO config;

    /**
     * 抽奖活动关联的支付订单
     */
    private Long lotteryDrawPaymentOrderId;


    /**
     * 银行活动二维码id
     */
    private Long marketingBankActivityQrCodeId;


    /**
     * 客户端IP
     */
    private Long clientIp;


    /**
     * 客户业务(可以多选) 0:基金 1:保险 2:理财
     */
    private List<Integer> clientServes;

    /**
     * 附件url集合
     */
    private List<String> customerPictureUrl;

    /**
     * 金额
     */
    private BigDecimal customerFaceValue;

    /**
     * 审核id
     */
    private Long marketingActivitiesReviewId;

    /**
     * 发放个数
     */
    private Integer batchSize;

    /**
     * 活动模式，0-购买商品 ，1-免费领券 2-抽奖 3-大客户发券
     */
    private Integer activityMode;

    /**
     * 资格校验验证码
     */
    private String validMsgCode;

    /**
     * 消费卡类型
     */
    private String cardIndex;

    /**
     * 用户选择的卡券条码
     */
    private String selectChargeCode;

    /**
     * 奖品发放形式
     */
    private MarketingPrizeGrantType grantType;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 奖品
     */
    private MarketingBankActivityPrizeSku prizeSku;

    /**
     * 个人备注
     */
    private String customRemark;

    /**
     * 分享人用户id
     */
    private Long inviterUserId;

    /**
     * 银行活动次数来源
     */
    private MarketingBankActivityNumSourceEnum activityNumSource;

    /**
     * 市场价（展示面额）
     */
    private BigDecimal marketPrice;

    /**
     * 活动信息
     */
    private MarketingBankActivityDTO bankActivityDTO;


    /**
     * 订单种类
     */
    private MarketingBankActivityOrderType orderKind;

    /**
     * 资格校验内容
     */
    private String qualificationContent;

    /**
     * 信息收集手机号
     */
    private String gatherInfoMobile;

    /**
     * 信息收集备注
     */
    private String gatherInfoRemark;

    /**
     * 商户信息收集
     */
    private MarketingBankGatherInfoSaveDTO infoSaveDTO;



    /**
     * 是否必须使用优惠券才可以下单
     */
    private Boolean mustUseCoupon;

    /**
     * 游戏分数记录id
     */
    private Long scoreRecordId;


    /**
     * 油卡Id
     */
    private Long oilCardId;

    /**
     * 活动口令
     */
    private String activityPassword;

    /**
     * openId
     */
    private String openId;

    /**
     * 发送批次
     */
    private Long sendBatchId;


    /**
     * 大客户自定义选项内容
     */
    private List<MarketingBankCustomBusinessDetailItemSaveDTO> customBusinessDetailItems;

    //大客户经理单次操作订单总数量
    private Integer batchOrderNum ;

    /**
     * 大客户经理单次操作订单总额
     */
    private BigDecimal batchOrderAmount;


    /**
     * 网点手机号
     */
    private String customerPhone;

    /**
     * 发券手机号:大客户经理使用校验手机号
     */
    //private String validMobile;
    /**
     * 客户经理名称
     */
    private String customerName;

    /**
     * 银行活动二维码id
     */
    private Long bankActivityQrCodeId;
    /**
     * 银行网点名称
     */
    private String bankBranchName;

    /**
     * 发券人部门
     */
    private String issuerDepartment;

    /**
     * 自定义项内容
     */
    private String customItemContent;

    /**
     * 预测算结果项
     */
    private List<MarketingBankActivityCalculateResultDTO.MarketingBankCalculateItem> advanceCalculateResultItem;

    /**
     * 预生成审核id
     */
    private Long preMarketingActivitiesReviewId;

    /**
     * 订单生效时间
     */
    private Date orderEffectTime;

    /**
     * 订单失效时间
     */
    private Date orderExpireTime;

    /**
     * 客户信息配置项及填写内容
     */
    private List<MarketingBankActivityCustomBusinessMetaFormVO.MarketingBankActivityCustomerConfigItemFormDTO> customerConfigItems;

    /**
     * 客户业务信息
     */
    private String customBusinessInfo;
    /**
     * 客户信息
     */
    private String customInfo;


    /**
     * 大客户经理openId
     */
    private String currentUserOpenId;

    /**
     * token
     */
    private String tokenStr;

    /**
     * 到账账号(支付宝选择账号：邮箱或者手机号)
     */
    private String arriveAccount;

    /**
     * 答题记录id
     */
    private Long questionRecordId;

    public Integer getBatchSize() {
        //默认单次
        return Optional.ofNullable(batchSize).orElse(1);
    }
}
