package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/28
 * 订单回调
 */
@Data
public class OrderCallbackDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 微信订单号
     */
    private String wxOrderNo;
    /**
     * 支付总金额
     */
    private BigDecimal totalFee;
    /**
     * 支付现金
     */
    private BigDecimal cashFee;
    /**
     * 优惠总金额
     */
    private BigDecimal couponFee;
    /**
     * 微信优惠券Id
     */
    private String wxCouponId;
    /**
     * 微信优惠券优惠明细
     */
    private Map<String, BigDecimal> wxCouponDetail;
    /**
     * 支付类型
     */
    private OrderPaymentType paymentType;
    /**
     * 支付银行
     */
    private String bankType;

}
