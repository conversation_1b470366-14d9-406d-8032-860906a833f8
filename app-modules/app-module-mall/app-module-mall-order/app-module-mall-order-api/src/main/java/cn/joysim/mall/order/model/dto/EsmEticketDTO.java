package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityCouponDiscountType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityCouponMiniStatus;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingActivityCouponType;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.order.model.pojo.enums.OrderTicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketCouponTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class EsmEticketDTO extends OrderEsmEticketDTO implements Serializable {


    /**
     * 订单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 电子券产品id
     */
    private Integer eticketProductId;

    /**
     * 同一个产品下的券列表
     */
    //List<CzfwTicketDetailParamsDTO.TicketDetailParamsItem> ticketList;

    /**
     * 券类型：0：班马，1：车主
     */
    //private Integer ticketSource;

    /**
     * 卡券类型：
     * 0：信用卡专属（斑马）
     * 1：车主服务
     * 2：优惠券
     * 3：其他
     * 4:全部
     */
    //private TicketCouponTypeEnum ticketSource;

    /**
     * 车主券状态码：
     * 0：正常
     * 1：已作废
     * 2：已消费
     * 3：已过期
     * 4：待激活
     * 5：服务中
     * 6：已升级
     */
    private Integer czfwState;
    /**
     * 预约地址
     */
    private String subscribeUrl;


    /**
     * 优惠券类型:
     * 0:微信
     * 1:满减
     * 2:折扣
     */
    private MarketingActivityCouponType couponType;

    /**
     * 优惠值（折扣为折，其他为优惠金额）使用price
     */
    //private BigDecimal couponValue;

    /**
     * 优惠条件（门槛）
     */
    private BigDecimal couponCondition;

    /**
     * 专区名称
     */
    private String arrondiName;

    /**
     * 产品专区id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long arrondiId;

    /**
     * 银行兑换专区Id
     */
    private Long exchangeId;


    /**
     * 优惠标签
     */
    private String couponTag;

    /**
     * 优惠类型
     */
    private MarketingActivityCouponDiscountType discountType;

    /**
     * 银行活动产品类型
     */
    private MarketingBankRechargeType bankRechargeType;

    /**
     * 是否需要激活
     */
    private Boolean needActivate;
    /**
     * 名字扩展
     */
    private String nameExt;

    /**
     * 优惠券类型 1 直减券（现金券） 2 折扣券， 6 直降券
     */
    private Integer merchandiseType;

    /**
     * 是否使用
     */
    private Boolean used;

    /**
     * 券说明
     */
    private String desc;


    /**
     * 使用门槛
     */
    private BigDecimal threshold;


    /**
     * 券的说明:满X元可用
     */
    private String comment;

    /**
     * 激活码
     */
    private String activateCode;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 券状态
     */
    private Integer ticketState;
}
