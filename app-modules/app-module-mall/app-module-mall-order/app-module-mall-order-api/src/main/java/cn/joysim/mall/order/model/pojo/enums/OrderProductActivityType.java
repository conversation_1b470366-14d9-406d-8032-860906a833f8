package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @author: linhs
 * @date: 2021/9/22
 * @desc: 活动订单类型
 */
public enum OrderProductActivityType implements ValueEnum {

    /**
     *非银行活动
     */
    NORMAL_ORDER(0, "非银行活动"),
    /**
     * 银行活动
     */
    BANK_ORDER(1, "银行活动"),
    /**
     * 全部订单
     */
    ALL_ORDER(2, "全部订单"),
    ;

    OrderProductActivityType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    public static OrderProductActivityType fromCode(Integer code) {
        for (OrderProductActivityType orderTypeEnum : OrderProductActivityType.values()) {
            if(orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }

        return null;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

}


