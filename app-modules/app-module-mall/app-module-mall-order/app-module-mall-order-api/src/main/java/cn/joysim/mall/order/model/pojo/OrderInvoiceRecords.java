package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.common.model.pojo.enums.OrderInvoiceStatus;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 开票记录表
 * @TableName tb_mall_order_invoice_records
 */
@TableName(value ="tb_mall_order_invoice_records")
@Data
public class OrderInvoiceRecords extends BaseEntity implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long orderId;


    /**
     * {@link cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum}
     */
    private Integer orderType;

    /**
     * 支付单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 开具发票时间
     */
    private Date applyInvoiceTime;

    /**
     * 用户实际支付金额
     */
    private BigDecimal cashTotalAmount;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNo;

    /**
     * 接收邮箱
     */
    private String receiverEmail;

    /**
     * 未导出开票
     */
    private OrderInvoiceStatus invoiceStatus;


    /**
     * 导出时间
     */
    private Date exportTime;

    /**
     * 导出用户
     */
    private String exportUser;


}
