package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/13
 * @desc: 易加油建单返回
 */
@Data
public class OrderEjyResultDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPaymentId;
    /**
     * 支付现金
     */
    private BigDecimal cashAmount;
}
