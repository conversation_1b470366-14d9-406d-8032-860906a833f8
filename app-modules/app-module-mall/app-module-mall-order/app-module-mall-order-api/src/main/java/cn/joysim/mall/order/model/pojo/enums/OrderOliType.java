package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 2.0.3
 * @author: linhs
 * @date: 2020/8/7
 * @desc: 油类型
 */
public enum OrderOliType implements ValueEnum {

    /**
     * 无
     */
    OLI_NULL(0, "无"),
    /**
     * 汽油
     */
    PETROL(1, "汽油"),
    /**
     * 柴油
     */
    DIESEL_OLI(2, "柴油"),

    ;

    @EnumValue
    private Integer code;
    private String text;

    OrderOliType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}

