package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/8/4
 * 团油订单状态
 */
public enum OrderTyStateEnum implements ValueEnum {


    /**
     * 未支付
     */
    UN_PAY(0, "未支付"),
    /**
     * 已支付
     */
    PAYED(1, "已支付"),
    /**
     * 退款中
     */
    REFUNDING(2, "退款中"),

    /**
     * 充值成功
     */
    SUCCESS(3, "充值成功"),

    /**
     * 已取消
     */
    CANCEL(4, "已取消"),

    /**
     * 已退款
     */
    REFUNDED(5, "已退款"),

    /**
     * 通知成功
     */
    PAYMENTS_SUCCESS(6, "通知成功"),

    /**
     * 退款失败
     */
    REFUNDED_FAIL(7, "退款失败"),

    /**
     * 通知失败
     */
    PAYMENTS_FAIL(8, "通知失败"),
    ;

    @EnumValue
    private Integer code;
    private String text;

    public static OrderTyStateEnum fromCode(Integer code) {
        for (OrderTyStateEnum stateEnum : OrderTyStateEnum.values()) {
            if(stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }

    OrderTyStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }
}
