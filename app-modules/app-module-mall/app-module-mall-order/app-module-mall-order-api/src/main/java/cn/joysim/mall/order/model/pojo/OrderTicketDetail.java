package cn.joysim.mall.order.model.pojo;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingPrizeGrantType;
import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 电子券订单明细
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_ticket_detail")
public class OrderTicketDetail {
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 产品Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;
    /**
     * 产品skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 面额
     */
    private Integer amount;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 发券状态
     */
    private TicketStateEnum state;

    /**
     * 电子券类型
     */
    private TicketTypeEnum ticketType;

    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 发券结果信息
     */
    private String message;
    /**
     * 发券返回信息
     */
    private String ticketInfo;
    /**
     * 发送批次Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long batchId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * sku结算名称
     */
    private String skuFinalName;
    /**
     * 充值编码
     */
    private String chargeCode;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 面值
     */
    private BigDecimal faceValue;
    /**
     * 销售价
     */
    private BigDecimal sellPrice;
    /**
     * 卡券供应商
     */
    private TicketSupplierEnum ticketSupplier;


    /**
     * 应用ID
     */
    private String appId;

    /**
     * openId
     */
    private String openId;

    /**
     * 奖品发放形式
     */
    private MarketingPrizeGrantType grantType;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;


    /**
     * 面向客户的发送卡券错误编码，可空
     * 例如：中行数字人民币"用户未开通钱包"等信息
     * 与发券结果“message”字段的区别与联系：
     * message面向对象是运营，涵盖的信息如:"用户未开通钱包"、"商户微信账户余额不足"、"系统后台配置错误”等
     * errorCode面向对象是用户，涉及到需要"用户未开通钱包"等需要用户参与解决的问题采用此字段告知用户
     */
    private OrderDetailErrorCode errorCode;

    /**
     * 发券商户号
     */
    private String sendMchId;


    /**
     * 激活码
     */
    private String activateCode;
}
