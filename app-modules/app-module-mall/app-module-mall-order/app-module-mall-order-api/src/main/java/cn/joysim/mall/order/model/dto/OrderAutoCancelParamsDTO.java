package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2024/3/29
 * @desc: 自动取消订单参数
 */
@Data
@Accessors(chain = true)
public class OrderAutoCancelParamsDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;
    /**
     * 操作人
     */
    private String operatorMan;

    /**
     * 备注
     */
    private String remark;



}
