package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 充值运营商
 * <AUTHOR>
 * @date 2020/7/24
 */
public enum RechargeOperator implements ValueEnum {
    /**
     * 三大运营商
     */
    CMCC(0, "移动"),
    CUCC(1, "联通"),
    CTCC(2, "电信"),

    CBN(3,"广电");

    RechargeOperator(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据名称转枚举
     * @param text
     * @return cn.joysim.mall.order.model.pojo.enums.RechargeOperator
     * <AUTHOR>
     * @date 2020/8/26
     */
    public static Integer getCodeByText(String text) {
        //去首尾空格
        if (text!=null && !"".equals(text)){
            text= text.trim();
            switch (text) {
                case "移动":
                    return RechargeOperator.CMCC.getCode();
                case "联通":
                    return RechargeOperator.CUCC.getCode();
                case "电信":
                    return RechargeOperator.CTCC.getCode();
                case "广电":
                    return RechargeOperator.CBN.getCode();
                default:
                    return RechargeOperator.CMCC.getCode();
            }
        }else {
            return null;
        }

    }

}
