package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/09/09
 */
@Data
public class EsmTicketDetailDTO implements Serializable {

    @JsonProperty("result")
    private Integer result;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("message")
    private String message;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("instructions")
        private String instructions;
        @JsonProperty("code")
        private String code;
        @JsonProperty("productId")
        private Integer productId;
        @JsonProperty("eticketProductId")
        private Integer eticketProductId;
        @JsonProperty("description")
        private String description;
        @JsonProperty("productName")
        private String productName;
        @JsonProperty("expiredTime")
        private String expiredTime;
        @JsonProperty("sendTime")
        private String sendTime;
        @JsonProperty("usedTimes")
        private Integer usedTimes;
        @JsonProperty("price")
        private Integer price;
        @JsonProperty("totalTimes")
        private Integer totalTimes;
        @JsonProperty("shopIds")
        private String shopIds;
        @JsonProperty("categoryValue")
        private Integer categoryValue;
        @JsonProperty("shops")
        private Map<String, Object> shops;
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("enterpriseId")
        private Integer enterpriseId;
        @JsonProperty("state")
        private Integer state;
        @JsonProperty("category")
        private String category;
        @JsonProperty("enterpriseName")
        private String enterpriseName;

    }

}
