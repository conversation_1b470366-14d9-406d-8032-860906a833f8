package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/5
 * @desc:
 */
@Data
@Accessors(chain = true)
public class OrderBankActivityLotteryDrawHandleDTO implements Serializable {

    private Long paymentOrderId;

    private String orderIp;

    /**
     * 银行活动二维码id
     */
    private Long marketingBankActivityQrCodeId;

    /**
     * 分享人用户id
     */
    private Long inviterUserId;

    /**
     * 被邀请人手机号
     */
    private String inviteeMobile;

    /**
     * 银行活动次数来源
     */
    private MarketingBankActivityNumSourceEnum activityNumSource;

    /**
     * 被邀请人的支付校验订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeOrderId;


    /**
     * 资格校验内容
     */
    private String qualificationContent;
}
