package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version: 3.2.0
 * @author: linhs
 * @date: 2021/5/26
 * @desc: 车主服务获取详情传参
 */
@Data
public class CzfwTicketDetailParamsDTO implements Serializable {

    /**
     * 参数列表
     */
    private List<TicketDetailParamsItem> items;

    /**
     *经度
     */
    private Double pointLng;
    /**
     *维度
     */
    private Double pointLat;

    /**
     * 产品ID
     */
    private String productId;

    @Data
    public static class TicketDetailParamsItem implements Serializable {
        /**
         * 券码
         */
        private String code;

        /**
         * 券id
         */
        private String ticketId;
    }


}
