package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户订单消费明细
 * <AUTHOR>
 * @date 2020/6/3
 */
@Data
public class UserConsumerDetail implements Serializable {
    /**
     * 总消费金额
     */
    private BigDecimal settlementAmount;
    /**
     * 订单总数
     */
    private Integer orderNum;
    /**
     * 最后消费时间
     */
    private Date lastOrderTime;
    /**
     * 客单价
     */
    private BigDecimal perCustomerTransaction;
}
