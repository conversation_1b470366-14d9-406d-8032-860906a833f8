package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/6/6 15:39
 */
@Data
public class BocSuperBenefitsRecordDTO implements Serializable {

    private Long orderId;
    /**
     * SKU_id
     */
    private Long skuId;

    /**
     * tb_mall_order_ticket_detail的Id
     */
    private Long detailId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 下单时间
     */
    private Date orderTime;


    private Integer state;

    /**
     * {@link cn.joysim.mall.marketing.model.pojo.enums.MarketingOrderTypeEnum}
     * 订单类型
     */
    private Integer orderType;

    /**
     * 电子券类型/奖品类型
     * {@link cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum}
     */
    private Integer ticketType;

    /**
     * 充值手机号
     */
    private String rechargeAccount;

    /**
     * 面向客户错误编码
     */
    private OrderDetailErrorCode errorCode;

    /**
     * prizeSkuId
     */
    private Long prizeSkuId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 支付单号
     */
    private Long orderPaymentId;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;

}
