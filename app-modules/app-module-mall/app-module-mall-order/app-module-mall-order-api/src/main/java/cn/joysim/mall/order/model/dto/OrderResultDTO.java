package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/28
 * 下单返回结果
 */
@Data
@NoArgsConstructor
public class OrderResultDTO implements Serializable {

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPaymentId;
    /**
     * 支付现金
     */
    private BigDecimal cashAmount;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * skuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;
    /**
     * 获得总班点
     */
    private BigDecimal obtainTotalBanDian;
    /**
     * 订单号
     */
    private List<String> orderIdList;
    /**
     * 激活订单
     */
    private Boolean activeOrder;

    /**
     * 订阅消息模板id
     */
    private List<String> subscribeTempId;

    /**
     * 中奖id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long marketingBankActivityWinningPrizeId;
    /**
     * 使用津贴
     */
    private BigDecimal useAllowance;

    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 支付路由
     */
    private String paymentUrl;


    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 阿里人机验证返回码（默认，异常的已经注解中拦截）
     */
    private Integer nvcCode = 200;

    /**
     * 是否跳转小程序
     * 默认是
     */
    private Boolean jumpMini;

    /**
     * 已注册手机号
     */
    private String registeredMobile;

    /**
     * 未注册手机号
     */
    private String unregisteredMobile;

    /**
     * 充值类型
     */
    private Integer rechargeType;

    public OrderResultDTO(Long paymentOrderId, BigDecimal cashAmount, BigDecimal obtainTotalAllowance, List<String> orderIdList, Boolean activeOrder) {
        this.orderPaymentId = paymentOrderId;
        this.cashAmount = cashAmount;
        this.obtainTotalAllowance = obtainTotalAllowance;
        this.orderIdList = orderIdList;
        this.activeOrder = activeOrder;
        this.useAllowance=BigDecimal.ZERO;
        this.discountTotalAmount=BigDecimal.ZERO;
        this.jumpMini=true;
    }

}
