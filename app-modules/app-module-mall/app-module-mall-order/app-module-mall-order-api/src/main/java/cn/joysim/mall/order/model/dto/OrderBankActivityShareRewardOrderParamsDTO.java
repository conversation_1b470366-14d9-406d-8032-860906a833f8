package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.MallMarketingBankActivityShareRewardSku;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/2/3
 * @desc: 初始化分享奖励订单参数
 */
@Data
@Accessors(chain = true)
public class OrderBankActivityShareRewardOrderParamsDTO implements Serializable {


    private Long activityId;

    private Long userId;

    private MallMarketingBankActivityShareRewardSku shareRewardSku;

    /**
     * 用户下单请求ip
     */
    private String orderIp;


    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;
}
