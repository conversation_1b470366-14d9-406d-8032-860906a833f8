package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderOliType;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.Data;
import org.springframework.format.annotation.NumberFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/3/14
 * @desc: 易加油下单
 */
@Data
public class OrderEjySaveDTO implements Serializable {
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 充值账号
     */
    private String phoneNumber;
    /**
     * 卡券类型
     */
    private TicketTypeEnum ticketType;

    /**
     * 油站 id
     */
    private Integer stationId;

    /**
     * 油站名称
     */
    private String stationName;
    /**
     * 油号 id
     */
    private String oilId;
    /**
     * 油号
     */
    private String oilCode;
    /**
     * 1：汽油
     * 2：柴油
     */
    private OrderOliType oilType;
    /**
     * 油枪号
     */
    private Integer oilgunCode;

    /**
     * 易加油订单标识
     */
    private String orderSign;

    /**
     * 订单总金额
     */
    //private BigDecimal orderTotalAmount;
    /**
     * 现金支付总金额
     */
    //private BigDecimal cashTotalAmount;
    /**
     * 津贴支付总金额
     */
    //private BigDecimal allowanceTotalAmount;
    /**
     * 产品总金额
     */
    //private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    //private BigDecimal discountTotalAmount;

    /**
     * 易加油建单返回
     */
    private OrderEjyCreateOrderDTO ejyCreateOrderDTO;


    private String mchId;

}
