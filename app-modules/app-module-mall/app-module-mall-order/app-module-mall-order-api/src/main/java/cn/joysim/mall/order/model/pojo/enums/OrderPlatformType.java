package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/16
 * @desc: 订单平台分类
 */
public enum OrderPlatformType implements ValueEnum {
    BM(0, "班马"),
    BANK(1, "银行"),
    BANK_ALIPAY(2, "银行-支付宝");

    @EnumValue
    private Integer code;
    private String text;

    OrderPlatformType(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static OrderPlatformType fromCode(Integer code) {
        for (OrderPlatformType stateEnum : OrderPlatformType.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }

        return null;
    }
}
