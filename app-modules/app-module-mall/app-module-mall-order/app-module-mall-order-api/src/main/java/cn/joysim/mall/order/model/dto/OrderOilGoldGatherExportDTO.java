package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/5/18
 * @desc: 加油金汇总导出
 */
@Data
public class OrderOilGoldGatherExportDTO implements Serializable {

    /**
     * 开始时间
     */
    @ExcelField(title = "开始时间", sort = 1)
    private String startDate;
    /**
     * 结束时间
     */
    @ExcelField(title = "结束时间", sort = 2)
    private String endDate;
    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID", sort = 3)
    private String userId;
    /**
     * 手机号
     */
    @ExcelField(title = "手机号", sort = 4)
    private String mobile;
    /**
     * 加油金余额
     */
    @ExcelField(title = "加油金余额", sort = 5)
    private String oilGoldSurplus;
    /**
     * 客户收益
     */
    @ExcelField(title = "客户收益", sort = 6)
    private String userIncome;
    /**
     * 班马收益
     */
    @ExcelField(title = "班马收益", sort = 7)
    private String bmIncome;
}
