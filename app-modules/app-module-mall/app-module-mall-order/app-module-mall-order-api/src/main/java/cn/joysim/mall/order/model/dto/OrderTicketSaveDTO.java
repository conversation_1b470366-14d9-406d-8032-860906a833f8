package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingInviteType;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/1
 */
@Data
public class OrderTicketSaveDTO implements Serializable {

    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 充值账号
     */
    private String rechargeAccount;
    /**
     * 卡券商品skuId
     */
    private Long skuId;
    /**
     * 卡券类型
     */
    private TicketTypeEnum ticketType;
    /**
     * 抢购助力记录Id
     */
    private Long purchaseRecordId;
    /**
     * 邀请人用户Id
     */
    private Long invitorUserId;
    /**
     * 邀请Id
     */
    private String inviteId;
    /**
     * 邀请类型
     */
    private MarketingInviteType inviteType;


    private String appId;

    /**
     * 发券openid
     */
    private String openId;

    /**
     * 用户选择的卡券条码
     */
    private String selectChargeCode;

    /**
     * 订单领取有效期
     */
    private Integer orderReceiveDays;

    /**
     * 市场价（展示面额,随机立减金指定金额）
     */
    private BigDecimal marketPrice;

    /**
     * 企业结算价
     */
    private BigDecimal enterprisePrice;


    /****************以下三个参数仅用于车主升级发券*****************/
    /**
     * 升级目标权益Id
     */
    private Long targetProductId;
    /**
     * 升级补偿差价
     */
    private BigDecimal compensationPrice;
    /**
     * 源券码
     */
    private String sourceCode;

}
