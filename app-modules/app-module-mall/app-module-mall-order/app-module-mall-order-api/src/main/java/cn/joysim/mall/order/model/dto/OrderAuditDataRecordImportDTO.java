package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderAuditDataType;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderAuditDataRecordImportDTO {

    /**
     * 所属企业
     */
    @ExcelProperty(value = "所属企业")
    private String enterpriseName;



    /**
     * 订单类型文本
     */
    @ExcelProperty(value = "订单类型")
    private String auditDataTypeStr;


    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private BigDecimal orderAmount;


    /**
     * 电子券金额
     */
    @ExcelProperty(value = "电子券支付")
    private BigDecimal couponPayAmount;

    /**
     * 订单时间
     */
    @ExcelProperty(value = "订单时间")
    private Date orderTime;


    /**
     * 订单时间（年）
     */
    @ExcelProperty(value = "订单时间（年）")
    private String orderTimeYear;

    /**
     * 订单时间（月）
     */
    @ExcelProperty(value = "订单时间（月）")
    private String orderTimeMonth;


    /**
     * 金额类型
     */
    @ExcelProperty(value = "金额类型")
    private String amountTypeStr;
}
