package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.enums.LicensePlateType;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderInsFormDTO {


    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 车牌号码
     */
    private String licensePlate;


    /**
     * 交强险
     */
    private BigDecimal compulsoryTrafficInsurance;


    /**
     * 商业险金额
     */
    private BigDecimal businessInsurance;


    /**
     * 车船税金额
     */
    private BigDecimal vehicleTax;


    /**
     * 车牌类型
     */
    private LicensePlateType licensePlateType;

}
