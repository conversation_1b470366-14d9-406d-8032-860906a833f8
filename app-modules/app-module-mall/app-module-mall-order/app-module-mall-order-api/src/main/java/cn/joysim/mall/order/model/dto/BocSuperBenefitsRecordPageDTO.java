package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.jackson.serialize.AddFieldJsonSerializer;
import cn.joysim.common.model.pojo.enums.order.OrderDetailErrorCode;
import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankRechargeType;
import cn.joysim.mall.marketing.model.vo.MarketBankActivityMiniAppEcnyPacketPrizeVo;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7 15:39
 */
@Data
public class BocSuperBenefitsRecordPageDTO {

    /**
     * sku的图片
     */
    private String imgUrl;
    /**
     * sku名称
     */
    private String skuName;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单状态
     */
    private Integer state;


    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单状态-文本
     */
    private String stateText;

    /**
     * 有效期至
     */
    private Date expiredTime;

    /**
     * 子订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderTicketDetailId;

    @JsonSerialize(using = AddFieldJsonSerializer.class )
    private OrderDetailErrorCode errorCode;

    /**
     * 展示补发按钮
     */
    private Boolean showReissueBtn;

    @JsonUnwrapped
    private MarketBankActivityMiniAppEcnyPacketPrizeVo ecnyPacketPrizeVo;

    /**
     * prizeSkuId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prizeSkuId;

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPaymentId;

    /**
     * 领取过期时间
     */
    private Date receiveExpireDate;

    /**
     * 充值类型
     */
    private MarketingBankRechargeType rechargeType;

    /**
     * 活动产品使用规则
     */
    private String useRule;

    /**
     * 是否跳转小程序
     * 默认是
     */
    private Boolean jumpMini;

}
