package cn.joysim.mall.order.model.pojo.bm;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderWechatReceiveRecordType;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信代金券领取记录
 * 2022-5-11 扩展为：卡券待领取记录，不局限于微信代金券，主要用在大客户经理给用户发券时，记录未注册商城用户时的待领取记录
 * <AUTHOR>
 * @date 2022/05/17
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_wechat_coupon_receive_record")
public class OrderWechatCouponReceiveRecord extends BaseEntity {

    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 子订单Id
     */
    private Long detailId;

    /**
     * 到账手机号
     */
    private String rechargeMobile;

    /**
     * 领取状态
     */
    private Boolean receiveState;

    /**
     * 发券记录id
     */
    private Long recordId;

    /**
     * 待领取记录类型
     */
    private OrderWechatReceiveRecordType recordType;

    /**
     * 卡券类型
     */
    private TicketTypeEnum ticketType;
}
