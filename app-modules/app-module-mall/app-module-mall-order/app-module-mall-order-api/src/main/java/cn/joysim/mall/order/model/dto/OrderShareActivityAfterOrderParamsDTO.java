package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityNumSourceEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/2/8
 * @desc: 分享活动下单后参数
 */
@Data
@Accessors(chain = true)
public class OrderShareActivityAfterOrderParamsDTO implements Serializable {

    private Long activityId;


    /**
     * 银行活动次数来源
     */
    private MarketingBankActivityNumSourceEnum activityNumSource;

    /**
     * 被邀请人的支付校验订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeOrderId;


    /**
     * 邀请者的用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviterUserId;

    /**
     * 被邀请的用户Id(当前下单用户)
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long inviteeUserId;


    /**
     * 用户下单请求ip
     */
    private String orderIp;

    /**
     * 记录金额
     */
    private BigDecimal recordAmount;
}
