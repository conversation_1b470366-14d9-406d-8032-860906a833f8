package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 卡券平台
 * <AUTHOR>
 * @date 2021/03/26
 */

public enum TicketPlatform implements ValueEnum {
    /**
     * ESM
     */
    ESM(0, "ESM"),
    /**
     * 车主服务
     */
    CZFW(1, "车主服务"),
    /**
     * 微信
     */
    WECHAT(2, "微信"),
    /**
     * 易加油
     */
    EJY(3, "易加油"),
    /**
     * 支付宝
     */
    ALIPAY(4, "支付宝"),

    /**
     * ESM
     */
    BM_MALL(5, "班马商城"),
    /**
     * E代驾
     */
    EDJ(6, "E代驾"),
    /**
     * E券
     */
    E_QUAN(7, "E券"),
    /**
     *银行商城
     */
    E_LIFE(8, "银行商城"),
    /**
     *渡渡鸟
     */
    T_DUDU_BIRD(9,"渡渡鸟"),
    /**
     *美团
     */
    T_MEI_TUAN(10,"美团"),


    /**
     * 广东中行省行
     */
    T_GD_BOC_PROVINCIAL_BANK(12, "广东中行省行"),

    /**
     * 四川中行省行
     */
    T_SC_BOC_PROVINCIAL_BANK(13, "四川中行省行"),


    /**
     * 湖南工行省行
     */
    T_HN_ICBC_PROVINCIAL_BANK(14,"湖南工行省行"),

    /**
     * 充值中心
     */
    SASS_RC(15,"充值中心")
    ;

    TicketPlatform(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }
}
