package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2022/7/25 10:41
 */
public enum CorpTagGroupMode implements ValueEnum {

    /**
     * 选择
     */
    CHOOSE(0,"选择"),
    /**
     * 新增
     */
    CREATE(1,"新增")
    ;

    CorpTagGroupMode(Integer code, String text){
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;

    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static CorpTagGroupMode fromCode(Integer code) {
        for (CorpTagGroupMode value : CorpTagGroupMode.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }

}
