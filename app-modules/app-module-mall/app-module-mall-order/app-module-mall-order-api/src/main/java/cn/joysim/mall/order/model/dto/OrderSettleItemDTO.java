package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.product.model.dto.ProductSKUImgDTO;
import cn.joysim.mall.product.model.pojo.enums.CategoryClassify;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: linhs
 * @date: 2020/2/16
 * @desc: 结算项（每个商品以及购买数量）
 */
@Data
public class OrderSettleItemDTO implements Serializable {

    @JsonSerialize(using= ToStringSerializer.class)
    private Long skuId;
    /**
     * 商品Id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long spuId;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 购买价格
     */
    private BigDecimal price;
    /**
     * 图片
     */
    private List<ProductSKUImgDTO> skuImgList;

    /**
     * 规格属性名称串
     */
    private String specifications;
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount = BigDecimal.ZERO;
    /**
     * 产品价格
     */
    private BigDecimal productPrice;
    /**
     * 分类的分类
     */
    private CategoryClassify categoryClassify;
    /**
     * 用户自定义类别编码
     */
    private Integer customCategoryCode;

}
