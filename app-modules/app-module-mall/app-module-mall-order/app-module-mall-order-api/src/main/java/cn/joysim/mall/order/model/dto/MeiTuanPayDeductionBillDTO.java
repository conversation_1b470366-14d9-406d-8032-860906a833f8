package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.mall.order.converter.easyExcel.MeiTuanBigDecimalNumberConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/11 14:16
 */
@Data
public class MeiTuanPayDeductionBillDTO {

    /**
     * 1、支付
     *
     * 2、退款
     */
    @ExcelProperty(order = 1)
    public String type;

    @ExcelProperty("活动ID")
    public String campaignId;

    @ExcelProperty("活动名称")
    public String campaignName;

    @ExcelProperty("支付银行")
    public String bankName;

    @ExcelProperty("支付通道")
    public String payChannelName;

    /**
     * 账单类型=【支付】时，该字段指：支付成功时间
     *
     * 账单类型=【退款】时，该字段指：退款成功时间
     */
    @ExcelProperty(value = "交易成功时间",converter = ConverterDateTime.class)
    public Date sucTime;


    /**
     * 账单类型=【退款】时，该字段指原支付单金额
     */
    @ExcelProperty(value = "支付单金额",converter = MeiTuanBigDecimalNumberConverter.class)
    public BigDecimal payMoney;

    /**
     * 账单类型=【支付】时，该字段指：支付优惠金额
     *
     * 账单类型=【退款】时，该字段指：原活动优惠金额
     */
    @ExcelProperty(value = "活动优惠金额",converter = MeiTuanBigDecimalNumberConverter.class)
    public BigDecimal reduceMoney;

    /**
     * 账单类型=【支付】时，该字段指：银行卡支付金额
     *
     * 账单类型=【退款】时，该字段指：银行卡退款金额
     */
    @ExcelProperty(value = "银行卡交易金额",converter = MeiTuanBigDecimalNumberConverter.class)
    public BigDecimal bankMoney;

    /**
     *
     * 账单类型=【支付】时，该字段赋值「-」
     */
    @ExcelProperty(value = "优惠实际退款金额",converter = MeiTuanBigDecimalNumberConverter.class)
    public BigDecimal refundMoney;

    @ExcelProperty("卡的前六后四位")
    public String first6last4;

    @ExcelProperty("请求银行支付的流水号")
    public String reqBankTradeNo;

    @ExcelProperty("银行返回流水号")
    public String repBankTradeNo;

    @ExcelProperty("银行卡ID")
    public String bankcardId;

}
