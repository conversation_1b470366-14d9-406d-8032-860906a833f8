package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.ShareRewardRule;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/3 11:48
 */
@Data
public class ActivityInviteRewardMiniPageDTO {


    /**
     * 奖励记录Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 充值账户
     */
    private String userMobile;

    /**
     * 奖励方案
     */
    private ShareRewardRule shareRewardRule;



    /**
     * 订单Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;


    /**
     * 发放时间
     */
    private Date sendTime;

    /**
     * 奖励订单
     */
    private Order order;


    /**
     * 奖励次数
     */
    private Num num;




    @Data
    @Builder
    public static class Num{

        /**
         * 成功邀请X个用户
         */
        private Integer inviteNum;

        /**
         * 抽奖次数+X
         */
        private Integer rewardNum;

        /**
         * 发放时间
         */
        private Date sendTime;

    }



    @Data
    @Builder
    public static class Order{

        /**
         * 订单类型
         */
        private OrderTypeEnum orderType;

        /**
         * 订单状态
         */
        private Integer orderState;

        /**
         * 订单状态文本
         */
        private String orderStateText;

        /**
         * 发放时间
         */
        private Date sendTime;


        /**
         * 卡券类型的订单
         */
        @JsonUnwrapped
        private Coupon coupon;



    }

    @Data
    @Builder
    public static class Coupon{

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 面值
         */
        private BigDecimal faceValue;
    }
}
