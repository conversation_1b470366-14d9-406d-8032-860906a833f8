package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/17
 * 支付状态/退款状态回调通知请求参数
 */
@Data
public class OrderEjyUserOrderDTO implements Serializable {
    /**
     * 易加油内部订单流水号
     */
    private String orderId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名字
     */
    private String city;

    /**
     * 油站名称
     */
    private String stationName;

    /**
     * 油号名称
     */
    private String oilCode;

    /**
     * 机显金额
     */
    private String originalCost;

    /**
     * 优惠券金额
     */
    private String couponMoney;

    /**
     * 用户实际支付金额
     */
    private String orderSum;

    /**
     * 加油升数
     */
    private String oilMass;

    /**
     * 1.微信 2.支付宝
     */
    private String payType;

    /**
     * 支付/退款时间
     */
    private String dateTime;

    /**
     * 1-支付 2-退款
     */
    private int status;

    /**
     * appkey 加密使用
     */
    private String ak;

    /**
     * 32 位随机字母数字组合
     */
    private String nonce;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 签名
     */
    private String sign;

    /**
     * 油站id
     */
    private String stationId;
    /**
     * 优惠券id
     */
    private String couponId;
    /**
     * 用户优惠券号码，唯一号码
     */
    private String userMerchandiseId;
    /**
     * 油站挂牌价
     */
    private String stationPrice;
    /**
     * 最终享受单价
     */
    private String discountPrice;
    /**
     * 国家价
     */
    private String countryPrice;
    /**
     * H5传入的P端自定义参数重新传回给P端
     */
    private String outState;

}
