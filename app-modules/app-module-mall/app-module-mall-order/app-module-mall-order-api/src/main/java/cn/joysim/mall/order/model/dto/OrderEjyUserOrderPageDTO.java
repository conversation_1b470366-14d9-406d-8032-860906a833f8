package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderEjyStateEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderTyStateEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/03/17
 * 易加油订单列表参数
 */
@Data
public class OrderEjyUserOrderPageDTO implements Serializable {
    /**
     * 易加油内部订单流水号
     */
    private String orderId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名字
     */
    private String city;

    /**
     * 油站名称
     */
    private String stationName;

    /**
     * 油号名称
     */
    private String oilCode;

    /**
     * 机显金额
     */
    private String originalCost;

    /**
     * 优惠券金额
     */
    private String couponMoney;

    /**
     * 用户实际支付金额
     */
   /* private String orderSum;*/

    /**
     * 加油升数
     */
    private String oilMass;

    /**
     * 1.微信 2.支付宝
     */
    private String payType;

    /**
     * 退款时间
     */
    private String dateTime;;

    /**
     * 1-支付 2-退款
     */
    private int status;

    /**
     * 油站id
     */
    //private String stationId;
    /**
     * 优惠券id
     */
    //private String couponId;
    /**
     * 用户优惠券号码，唯一号码
     */
    //private String userMerchandiseId;
    /**
     * 油站挂牌价
     */
    //private String stationPrice;
    /**
     * 最终享受单价
     */
    //private String discountPrice;
    /**
     * 国家价
     */
    //private String countryPrice;
    /**
     * H5传入的P端自定义参数重新传回给P端
     */
    //private String outState;

    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 下单时间
     */
    private Date orderTime;

    private OrderEjyStateEnum ejyState;

    /**
     * 订单状态
     */
    private String stateStr;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 现金总金额，用户实际支付金额
     */
    private BigDecimal cashTotalAmount;
    /**
     * 津贴支付总金额
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;

    /**
     * 易加油订单标识
     */
    private String orderSign;

    private OrderTyStateEnum tyState;

    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;



}
