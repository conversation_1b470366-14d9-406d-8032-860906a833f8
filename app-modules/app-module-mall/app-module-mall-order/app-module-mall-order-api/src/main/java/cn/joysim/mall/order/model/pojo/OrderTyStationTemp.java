package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 团油油站临时表
 * @TableName tb_mall_order_ty_station_temp
 */
@TableName(value ="tb_mall_order_ty_station_temp")
@Data
public class OrderTyStationTemp extends BaseEntity implements Serializable {

    /**
     * 团油油站Id
     */
    private String gasId;


    /**
     * 查询Id,防止并发时会导致误删数据
     */
    private Long queryId;

}