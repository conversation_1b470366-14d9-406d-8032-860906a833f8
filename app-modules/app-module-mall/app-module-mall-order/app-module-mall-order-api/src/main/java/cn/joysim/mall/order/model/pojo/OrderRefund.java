package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderRefundStateEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/10
 */
@Data
@EqualsAndHashCode
@TableName("tb_mall_order_refund")
public class OrderRefund extends BaseEntity {

    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 支付单号
     */
    private Long paymentOrderId;
    /**
     * 微信支付单号
     */
    private String wxOrderId;
    /**
     * 微信退款单号
     */
    private String wxRefundId;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款状态
     */
    private OrderRefundStateEnum state;
    /**
     * 退款成功
     */
    private Date refundTime;
    /**
     * 备注
     */
    private String remark;

}
