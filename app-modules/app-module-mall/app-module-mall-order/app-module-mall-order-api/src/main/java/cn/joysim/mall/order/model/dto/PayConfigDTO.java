package cn.joysim.mall.order.model.dto;

import cn.joysim.common.util.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/11/11
 * 支付配置
 */
@Data
public class PayConfigDTO implements Serializable {

    /**
     * 小程序AppId
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 支付签名key
     */
    private String paySignKey;
    /**
     * 支付回调
     */
    private String notifyUrl;
    /**
     * 证书路径
     */
    private String certPath;
    /**
     * 退款回调
     */
    private String refundNotifyUrl;


    public static void main(String[] args) {
        PayConfigDTO configDTO = new PayConfigDTO();
        configDTO.setAppId("wxbef37530b93f951b");
        configDTO.setCertPath("/home/<USER>/svr/jdff-mall/conf/apiclient_cert.p12");
        configDTO.setMchId("1561140291");
        configDTO.setNotifyUrl("https://t6.yesm.cn/api/mall/mini/order/payNotify");
        configDTO.setPaySignKey("Jin832J83jLhf883GB7sd8xc8v3273Bv");
        configDTO.setRefundNotifyUrl("https://t6.yesm.cn/api/mall/mini/order/refundNotify");
        System.out.println(JSON.toJSONString(configDTO));
    }
}
