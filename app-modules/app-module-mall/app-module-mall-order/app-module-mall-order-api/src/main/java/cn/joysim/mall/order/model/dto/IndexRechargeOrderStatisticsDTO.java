package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页订单统计
 *
 * <AUTHOR>
 * @date 2021/3/1
 */
@Data
public class IndexRechargeOrderStatisticsDTO implements Serializable {

    /**
     * 订单金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 日期
     */
    private String orderTime;
    /**
     * 充值类型
     */
    private RechargeTypeEnum rechargeType;
}
