package cn.joysim.mall.order.model.pojo;

import cn.hutool.core.collection.CollectionUtil;
import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @version: 2.5.2
 * @author: linhs
 * @date: 2022/3/14
 * @desc: 易加油
 */
@Data
@TableName("tb_mall_order_ejy_detail")
public class OrderEjyDetail extends BaseEntity {

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;


    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 油站 id
     */
    private Integer stationId;

    /**
     * 油站名称
     */
    private String stationName;
    /**
     * 油号 id
     */
    //private String oilId;
    /**
     * 油号
     */
    private String oilCode;
    /**
     * 1：汽油
     * 2：柴油
     */
    private OrderOliType oilType;
    /**
     * 油枪号
     */
    private Integer oilGunCode;

    /**
     * 卡券供应商
     */
    private TicketSupplierEnum ticketSupplier;

    /**
     * 发券状态
     */
    private TicketStateEnum state;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 发券结果信息
     */
    private String message;
    /**
     * 发券返回信息
     */
    //private String ticketInfo;

    /**
     * 电子券类型
     */
    private TicketTypeEnum ticketType;

    /**
     * 易加油订单标识
     */
    private String orderSign;

    /**
     * 油量，单位升，保留两位小数
     */
    private BigDecimal oilMass;

    /**
     * 用户最终享受单价
     */
    private String discountPrice;

    /**
     * 手机号码
     */
    //private String phone;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名字
     */
    private String city;


    /**
     * 机显金额
     */
    private String originalCost;

    /**
     * 优惠券金额
     */
    private BigDecimal couponMoney;

    /**
     * 用户实际支付金额
     */
    //private String orderSum;


    /**
     * 1.微信 2.支付宝
     */
    //private OrderEjyOrderDetailPayTypeEnum payType;

    /**
     * 支付/退款时间
     */
    //private String dateTime;

    /**
     * 1-支付 2-退款
     */
    //private OrderEjyOrderDetailStatusEnum status;

    /**
     * appkey 加密使用
     */
    //private String ak;

    /**
     * 32 位随机字母数字组合
     */
    //private String nonce;

    /**
     * 时间戳
     */
    //private String timestamp;

    /**
     * 签名
     */
    //private String sign;

    /**
     * 优惠券id
     */
    private String couponId;
    /**
     * 用户优惠券号码，唯一号码
     */
    private String userMerchandiseId;
    /**
     * 油站挂牌价
     */
    private String stationPrice;

    /**
     * 国家价
     */
    private String countryPrice;
    /**
     * H5传入的P端自定义参数重新传回给P端
     */
    private String outState;


    /**
     * 易加油端支付或退款时间
     */
    private String refundTime;
    /**
     * 是否回调
     */
    //private Boolean callBack;

    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;

}
