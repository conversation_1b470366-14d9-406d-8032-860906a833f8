package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPaymentCallBackStateEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/22
 */
@Data
public class OrderPaymentDocumentDTO implements Serializable {

    /**
     * 回调状态(0:未回调,1:回调成功,2:回调失败)
     */
    private OrderPaymentCallBackStateEnum state;
    /**
     * 支付金额
     */
    private BigDecimal totalFee;
    /**
     * 支付现金
     */
    private BigDecimal cashAmount;
    /**
     * 微信优惠金额
     */
    private BigDecimal wxCouponFee;
    /**
     * 支付积分
     */
    private Integer integral;
    /**
     * 支付成功时间
     */
    private Date paymentSuccessTime;
    /**
     * 微信支付流水
     */
    private String wxOrderNo;
    /**
     * 优惠券
     */
    private String couponInfo;
    /**
     * 微信优惠券Id
     */
    private String wxCouponId;
    /**
     * 支付方式
     */
    private OrderPaymentType paymentType;

    private String paymentTypeStr;

    /**
     *
     * 银联流水号
     **/
    private String  unionOrderId;

    /**
     * 景心优惠
     */
    private String joysimPreferential;
    /**
     * 商户贴息
     */
    private String joysimAccrual;
    /**
     * 分期情况
     */
    private String stageDetail;

    /**
     * 卡种
     */
    private String cardTy;

    /**
     * 优惠券抵扣
     */
    private String discountCouponInfo;

}
