package cn.joysim.mall.order.model.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/21
 * 订单收货信息
 */
@EqualsAndHashCode
@Data
@TableName("tb_mall_order_receiver_info")
public class OrderReceiverInfo{
    /**
     * 主键
     */
    @TableId
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 收货人
     */
    private String receiverName;
    /**
     * 收货地址
     */
    private String receiverAddress;
    /**
     * 收货人手机号码
     */
    private String receiverMobile;
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 物流单号
     */
    private String logisticsNum;
    /**
     * 签收时间
     */
    private Date signTime;
    /**
     * 发货时间
     */
    private Date sendTime;
    /**
     * 邮费
     */
    private BigDecimal postage;
}
