package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderWxActivityBillPageDTO implements Serializable {

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 券ID
     */
    private String couponId;

    /**
     * 优惠类型
     */
    private String discountType;

    /**
     * 优惠金额（核销面额）
     */
    private BigDecimal discountAmount;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 账单类型（交易类型）0:未知 1:支付 2:退款
     */
    private String billType;

    /**
     * 支付单号（支付宝交易号）
     */
    private String paymentOrderNumber;

    /**
     * 消费时间（核销时间）
     */
    private Date consumerTime;

    /**
     * 消费商户号
     */
    private String consumerBusinessAccount;


    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 银行返回流水号
     */
    private String bankResponseSerialNumber;

    /**
     * 消耗门店编码(微信支付)
     */
    private String wechatConsumerStoreCode;

    /**
     * 消耗门店编码(商家自有)
     */
    private String businessConsumerStoreCode;


}
