package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 
 * @TableName tb_mall_order_ticket_notify_log
 */
@TableName(value ="tb_mall_order_ticket_notify_log")
@Data
@Builder
public class OrderTicketNotifyLog extends BaseEntity implements Serializable {

    /**
     * tb_mall_order_esm_eticket.id
     */
    private Long ticketId;

}