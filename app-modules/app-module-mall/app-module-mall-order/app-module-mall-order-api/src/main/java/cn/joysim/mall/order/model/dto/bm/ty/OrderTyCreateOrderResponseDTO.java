package cn.joysim.mall.order.model.dto.bm.ty;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/8/3
 * @desc: 团油订单推送返回
 */
@Data
public class OrderTyCreateOrderResponseDTO implements Serializable {

    private Integer code;

    private String msg;

    public DataItem data;

    @Data
    public static class DataItem{
        private String url;
        private String jumpType;
    }
}
