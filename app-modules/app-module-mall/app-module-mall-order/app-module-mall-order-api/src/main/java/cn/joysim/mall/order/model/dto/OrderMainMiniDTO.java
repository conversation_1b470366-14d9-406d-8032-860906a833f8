package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.jackson.serialize.MobileDesensitizedConverter;
import cn.joysim.mall.order.model.pojo.enums.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/1
 */
@Data
public class OrderMainMiniDTO implements Serializable {
    /**
     * 订单号
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
    /**
     * 支付单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 支付现金
     */
    private BigDecimal cashTotalAmount;
    /**
     * 支付津贴
     */
    private BigDecimal allowanceTotalAmount;
    /**
     * 产品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 优惠总金额
     */
    private BigDecimal discountTotalAmount;
    /**
     * 邮费
     */
    private Integer postage;
    /**
     * 订单状态
     */
    private Integer state;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 实物订单明细
     */
    private List<OrderEntityDetailDTO> orderDetailList;
    /**
     * 卡券明细
     */
    private List<OrderTicketDetailDTO> ticketDetail;
    /**
     * 充值明细
     */
    private OrderRechargeDetailDTO rechargeDetail;
    /**
     * 团购订单
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;
    /**
     * 获得总津贴
     */
    private BigDecimal obtainTotalAllowance;
    /**
     * 激活状态
     */
    private OrderActiveState activeState;
    /**
     * 有效期
     */
    private Date effectiveTime;

    /**
     * 支付方式
     * 0：微信
     * 3：云闪付
     */
    private OrderPaymentType paymentType;

    /**
     * 分期总价
     **/
    private BigDecimal stagePrice;

    /**
     * 分期情况
     */
    private String stageDetail;


    /**
     * 油站名称
     */
    private String stationName;

    /**
     * 支付方式
     */
    private String paymentTypeStr;

    /**
     * 订单状态中文
     */
    private String stateStr;


    /**
     * 发券客户的手机号码
     */
    @JsonSerialize(converter = MobileDesensitizedConverter.class)
    private String mobile;


    /**
     * 赠单
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long handSelOrder;

    /**
     * 是否显示激活按钮，订单列表为false
     */
    private Boolean showActivateBtn;

    public String getStateStr(){
        switch (this.getOrderType()){
            case ENTITY_TYPE:
                return OrderEntityStateEnum.fromCode(this.getState()).getText();
            case TICKET_TYPE:
                if (this.getState()==1){
                    return "等待充值";
                }
                return OrderTicketStateEnum.fromCode(this.getState()).getText();
            case RECHARGE_TYPE:
                return OrderRechargeStateEnum.fromCode(this.getState()).getText();
            case EJY_TYPE:
                return OrderEjyStateEnum.fromCode(this.getState()).getText();
            case TY_TYPE:
                return OrderTyStateEnum.fromCode(this.getState()).getText();
        }

        return "";
    }
}
