package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 2.3.4
 * @author: linhs
 * @date: 2021/3/2
 * @desc: 提现数据统计
 */
@Data
public class OrderWithdrawCashStatisticsDTO implements Serializable {
    /**
     * 未到账订单金额
     */
    private BigDecimal processAmount;
    /**
     * 已到账订单金额
     */
    private BigDecimal arriveAmount;
    /**
     * 订单总金额
     */
    private BigDecimal total;

    /**
     * 提现开关
     * 0：关闭
     * 1：开启
     */
    private Integer makeCashSwitch;
}
