package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车主服务订单列表项
 */
@Data
public class OrderCZFWTicketDTO implements Serializable {

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    /**
     * 支付订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paymentOrderId;

    /**
     * 卡券类型名称
     */
    private String categoryName;
    /**
     * 卡券名称
     */
    private String skuName;

    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 到账账号
     */
    private String rechargeAccount;

    /**
     * 付款金额
     */
    private BigDecimal cashTotalAmount;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 订单状态
     */
    private Integer state;

    /**
     * 子订单状态
     */
    private Integer subState;

    private TicketTypeEnum ticketType;

    public String getCategoryName() {
        return ticketType == null ? "--" : this.ticketType.getText();
    }

}
