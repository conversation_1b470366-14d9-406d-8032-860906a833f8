package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/10
 * 油站列表
 */
@Data
public class OrderEjyStationDTO implements Serializable {

    /**
     * 油站id
     */
    String stationId;

    /**
     * 油站名称
     */
    String stationName;

    /**
     * 油站类型，1 中石油，2 中石化，3 壳牌，4 其他
     */
    int stationType;

    /**
     * 油站地址
     */
    String location;

    /**
     * 城市编号
     */
    int cityId;

    /**
     * 城市名称
     */
    String cityName;

    /**
     * 省份编号
     */
    int provinceId;

    /**
     * 省份
     */
    String provinceName;

    /**
     * 纬度，百度坐标系
     */
    String latitude;

    /**
     * 经度，百度坐标系
     */
    String longitude;

    /**
     * 油站组织
     */
    int stationOrgnization;

    /**
     * 电话
     */
    String phone;

    /**
     * 广告
     */
    List<OrderEjyStationAdvertDTO> adverts;

    /**
     * 发票类型
     */
    int invoiceType;

    /**
     * 评分
     */
    String starNum;

    /**
     * 价格
     */
    List<OrderEjyStationPriceDTO> prices;

    /**
     * 油站小图
     */
    String stationPic;

    /**
     * 油站大图
     */
    String stationBannerPic;

    /**
     * 地区
     */
    String district;

    /**
     * 地区
     */
    String isSupportStationInvoice;
}
