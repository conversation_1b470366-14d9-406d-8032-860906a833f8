package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.pojo.enums.MarketingBankActivityOrderType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/4/3
 * @desc: 订单活动信息
 */
@Data
public class OrderActivityDetailDTO implements Serializable {

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 兑换专区id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exchangeId;

    /**
     * 奖品订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prizeOrderId;

    /**
     * 支付订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long payOrderId;

    /**
     * 订单类型
     */
    private MarketingBankActivityOrderType orderType;


}
