package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
public enum OrderELifeCouponState implements ValueEnum {

    /**
     * 未使用
     */
    NOT_USED(0, "未使用"),
    /**
     * 已使用
     */
    USED(1, "已使用"),

    /**
     * 已过期
     */
    EXPIRE(2,"已过期"),

    /**
     * 发送失败
     */
    FAIL(3,"发送失败"),


    /**
     * 删除优惠券
     */
    DELETED(4,"删除");

    OrderELifeCouponState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static OrderELifeCouponState fromCode(Integer code) {
        for (OrderELifeCouponState value : OrderELifeCouponState.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
