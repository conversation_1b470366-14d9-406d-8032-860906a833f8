package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2023/12/22
 * @desc: 抽奖奖品轮播
 */
@Data
public class OrderLotteryDrawRewardResultDTO implements Serializable {

    /**
     * 中奖人数
     */
    private Integer rewardNum;

    /**
     * 中奖奖品
     */
    private List<OrderLotteryDrawRewardBannerDTO> rewardList;

}
