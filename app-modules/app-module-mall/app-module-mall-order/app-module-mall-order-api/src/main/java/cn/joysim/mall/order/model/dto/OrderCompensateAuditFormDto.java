package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.enums.TrueFalseStatus;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/2/21 16:29
 */
@Data
public class OrderCompensateAuditFormDto {

    /**
     * 审核结果
     */
    @NotNull
    private TrueFalseStatus auditResult;


    /**
     * 审核意见
     */
    @NotEmpty
    private String auditComment;

}
