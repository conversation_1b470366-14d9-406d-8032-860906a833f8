package cn.joysim.mall.order.model.dto;

import cn.joysim.common.annotation.ExcelField;
import cn.joysim.common.converter.easyExcel.ConverterDateTime;
import cn.joysim.common.converter.easyExcel.EnumToStringConverter;
import cn.joysim.mall.order.model.pojo.enums.OrderEjyOrderDetailPayTypeEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderEjyOrderDetailStatusEnum;
import cn.joysim.mall.order.model.pojo.enums.OrderEjyStateEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/7 14:56
 */
@Data
public class OrderEjyDetailExportDTO implements Serializable {
    /**
     * 易加油内部订单流水号
     */
    @ExcelProperty(value ="订单号")
    private String orderId;

    /**
     * 手机号码
     */
    @ExcelProperty(value ="手机号")
    private String phone;

    /**
     * 省份名称
     */
    @ExcelProperty(value ="省份")
    private String province;

    /**
     * 城市名字
     */
    @ExcelProperty(value ="城市")
    private String city;

    /**
     * 油站名称
     */
    @ExcelProperty(value ="油站名称")
    private String stationName;

    /**
     * 油号名称
     */
    @ExcelProperty(value ="油号名称")
    private String oilCode;

    /**
     * 机显金额
     */
    @ExcelProperty(value ="机显金额")
    private String originalCost;

    /**
     * 优惠券金额
     */
    @ExcelProperty(value ="优惠券金额")
    private BigDecimal couponMoney;



    /**
     * 加油升数
     */
    @ExcelProperty(value ="加油升数")
    private BigDecimal oilMass;

    /**
     * 1.微信 2.支付宝
     */
    @ExcelProperty(value ="支付方式")
    private String payType;

//    /**
//     * 支付/退款时间
//     */
//    @ExcelProperty(value ="支付/退款时间")
//    private String dateTime;

    /**
     * 1-支付 2-退款
     */
    @ExcelProperty(value ="状态",converter = EnumToStringConverter.class)
    private OrderEjyStateEnum ejyState;

    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long paymentOrderId;

    /**
     * 订单总金额
     */
    @ExcelProperty("订单总金额")
    private BigDecimal orderTotalAmount;
    /**
     * 现金总金额，用户实际支付金额
     */
    @ExcelProperty("实际支付金额")
    private BigDecimal cashTotalAmount;
    /**
     * 津贴支付总金额
     */
    @ExcelProperty("津贴抵扣")
    private BigDecimal allowanceTotalAmount;


    /**
     * 易加油订单标识
     */
    @ExcelProperty("订单标识")
    private String orderSign;

    /**
     * 下单时间
     */
    @ExcelProperty(value = "下单时间",converter = ConverterDateTime.class)
    private Date orderTime;

    /**
     * 退款时间
     */
    @ExcelProperty("退款时间")
    private String dateTime;


    /**
     * 班马手续费
     */
    @ExcelProperty("班马手续费")
    private BigDecimal bmServiceFee;

    /**
     * 企业总实扣⾦额
     */
    @ExcelProperty("企业总实扣金额")
    private BigDecimal totalEnterpriseRealAmount;
}
