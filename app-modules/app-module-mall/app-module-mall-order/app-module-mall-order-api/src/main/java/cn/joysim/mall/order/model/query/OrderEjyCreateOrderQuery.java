package cn.joysim.mall.order.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/11
 * @desc 创建订单请求参数
 */
@Data
public class OrderEjyCreateOrderQuery implements Serializable {

    /**
     * 易加油油站 ID
     */
    @NotBlank(message = "油站id必填")
    private Integer stationId;

    /**
     * 易加油油枪编号
     */
    @NotBlank(message = "加油油枪编号必填")
    private Integer oilgunCode;

    /**
     * 订单总金额，即用户输入金额，保留两位小数
     */
    @NotBlank(message = "订单总金额必填")
    private String totalAmount;

    /**
     * 创建订单的手机号码
     */
    @NotBlank(message = "创建订单的手机号码必填")
    private String phoneNumber;

    /**
     * 是否需要发票
     */
    private Boolean hadInvoice;

    /**
     * 发票抬头
     */
    private String invoiceHead;

    /**
     * 个人税号
     */
    private String invoiceNumber;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 用户优惠券编号
     */
    private String userCouponId;

    /**
     * 第三方订单号
     */
    private String outOrderSign;

}
