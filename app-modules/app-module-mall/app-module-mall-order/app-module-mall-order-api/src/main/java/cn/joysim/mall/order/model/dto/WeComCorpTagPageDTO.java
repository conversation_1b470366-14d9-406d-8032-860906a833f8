package cn.joysim.mall.order.model.dto;

import cn.joysim.common.converter.jackson.serialize.ValueEnumToStringConverter;
import cn.joysim.mall.order.converter.jackson.OrderTypeGroupSerialize;
import cn.joysim.mall.order.converter.jackson.WeComCorpTagConditionSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/25 16:18
 */
@Data
public class WeComCorpTagPageDTO implements Serializable {


    /**
     * id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;


    /**
     * 标签组Id
     */
    private String groupName;


    /**
     * 订单类型
     */
    @JsonSerialize(converter = OrderTypeGroupSerialize.class)
    private OrderTypeGroupDP orderTypes;

    /**
     * 条件
     */
    @JsonSerialize(converter = WeComCorpTagConditionSerialize.class)
    private List<WeComCorpTagConditionDP> weComCorpTagConditionDTOList;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;


}
