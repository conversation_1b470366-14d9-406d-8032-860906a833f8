package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 主订单分组与主订单联系
 * @TableName tb_mall_order_main_group_rel
 */
@TableName(value ="tb_mall_order_main_group_rel")
@Data
public class OrderMainGroupRel extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 分组Id
     */
    private Long groupId;

    /**
     * 主订单Id
     */
    private Long orderMainId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}