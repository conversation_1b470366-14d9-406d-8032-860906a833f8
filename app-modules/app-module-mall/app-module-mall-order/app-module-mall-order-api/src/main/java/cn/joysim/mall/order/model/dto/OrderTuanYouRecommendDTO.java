package cn.joysim.mall.order.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/9/5 10:41
 */
@Data
public class OrderTuanYouRecommendDTO implements Serializable {


    /**
     * 油站Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    /**
     * 油站名称
     */
    private String stationName;


    /**
     * 位置
     */
    private String location;


    /**
     * 折扣价
     */
    private BigDecimal discountPrice;


    /**
     * 纬度
     */
    private String latitude;


    /**
     * 经度
     */
    private String longitude;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 油号id,eg:92
     */
    private Integer oilNo;


    /**
     * 油号名称,eg:"92#"
     */
    private String oilName;

}
