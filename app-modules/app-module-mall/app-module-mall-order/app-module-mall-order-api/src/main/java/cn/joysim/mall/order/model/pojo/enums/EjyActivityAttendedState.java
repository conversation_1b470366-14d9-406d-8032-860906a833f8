package cn.joysim.mall.order.model.pojo.enums;

import cn.joysim.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/4/22 14:21
 */
public enum EjyActivityAttendedState implements ValueEnum {
    /**
     * 可领取
     */
    ATTEND_ABLE(1, "可领取"),
    /**
     * 已使用
     */
    USED(2, "没领，不能领"),
    /**
     * 已核销
     */
    ATTEND_DISABLE(3, "已领取，不可再领");

    @EnumValue
    private Integer code;
    private String text;

    EjyActivityAttendedState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }


    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }
}
