package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/03/11
 * 油站详情信息
 */
@Data
public class OrderEjyStationPriceInfoDTO implements Serializable {

    /**
     * 服务费
     */
    List<FeeRule> feeRules;

    /**
     * 油站每天单个用户优惠次数
     */
    int limitCount;

    /**
     * 用户当天已加油次数
     */
    int orderCount;

    /**
     * 价格
     */
    List<OrderEjyStationPriceDTO> prices;


    @Data
    public static class FeeRule {

        /**
         * 临界阈值，优惠大于等于这个值收取手续费
         */
        private Double threshold;

        /**
         * 	服务费
         */
        private Double feeValue;

    }

}
