package cn.joysim.mall.order.model.query;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Data
public class OrderPackageStatisticsQuery {

    /**
     * 下单开始时间
     */
    private String startDate;
    /**
     * 下单结束时间
     */
    private String endDate;
    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 包裹Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long packageId;
}
