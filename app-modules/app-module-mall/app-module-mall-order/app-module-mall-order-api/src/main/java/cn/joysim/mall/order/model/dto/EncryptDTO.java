package cn.joysim.mall.order.model.dto;

import cn.joysim.common.model.pojo.enums.EncryptTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/04/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncryptDTO implements Serializable {


    private EncryptTypeEnum encryptTypeEnum;

    /**
     * 公钥
     */
    public String publicKey;
    /**
     * 公钥
     */
    public String privateKey;
    /**
     * 时间戳
     */
    public String timestamp;
    /**
     * 版本号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    public Long version;

}
