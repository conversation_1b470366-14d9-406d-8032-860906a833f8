package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderRechargeStateEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeStateEnum;
import cn.joysim.mall.order.model.pojo.enums.RechargeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/6/2
 * @desc:
 */
@Data
public class OrderOilGoldRechargeRecordDTO implements Serializable {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单状态
     */
    private OrderRechargeStateEnum orderState;
    /**
     * 子订单状态
     */
    private RechargeStateEnum recordState;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 面值
     */
    private BigDecimal faceValue;

    /**
     * 充值类型(0：快充，1：慢充，2：娱乐，3：流量)
     */
    private RechargeTypeEnum rechargeType;

    /**
     * 子订单id
     */
    private Long detailId;
}
