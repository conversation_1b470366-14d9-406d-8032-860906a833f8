package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.marketing.model.dto.MarketingActivityDTO;
import cn.joysim.mall.marketing.model.dto.MarketingActivitySkuDTO;
import cn.joysim.mall.order.model.pojo.enums.OrderPaymentType;
import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import cn.joysim.mall.product.model.pojo.ProductSKU;
import cn.joysim.mall.product.model.pojo.ProductSupplier;
import cn.joysim.mall.user.model.dto.UserReceiverAddressDTO;
import cn.joysim.mall.user.model.pojo.MallUser;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/22
 * 订单处理数据
 */
@Data
public class OrderMainSaveDTO implements Serializable {

    /**
     * 下单用户
     */
    private MallUser mallUser;
    /**
     * 购物车
     */
    private ShoppingCartDTO shoppingCart;
    /**
     * 充值订单
     */
    private OrderRechargeSaveDTO orderRecharge;
    /**
     * 卡券订单
     */
    private OrderTicketSaveDTO ticketOrder;
    /**
     * 收货地址
     */
    private Long addressId;
    private UserReceiverAddressDTO userReceiverAddress;
    /**
     * 备注
     */
    private String remark;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 供应商
     */
    private ProductSupplier supplier;
    /**
     * 购买产品，用于临时缓存
     */
    private Map<Long, ProductSKU> productSkuMap;
    /**
     * 支付单号
     */
    private Long paymentOrderId;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 团购Id
     */
    private Long groupId;
    /**
     * 活动信息
     */
    private MarketingActivityDTO activity;
    /**
     * 活动产品
     */
    private Map<Long, MarketingActivitySkuDTO> activitySkuMap;
    /**
     * 分享人Id
     */
    private Long shareUserId;
    /**
     * 使用津贴
     */
    private Boolean useAllowance;
    /**
     * 使用优惠券
     */
    private Boolean useCoupon;
    /**
     * 支付类型
     */
    private OrderPaymentType paymentType;
    /**
     * 邀请人用户Id
     */
    private Long invitorUserId;
    /**
     * 邀请人用户Id
     */
    private Long cycleId;
    /**
     * 送礼中心订单
     */
    private Boolean sendGiftOrder;
    /**
     * 汕头建行活动
     */
    private OrderShantouCCBRechargeDTO shantouCCBRecharge;
    /**
     * 订单来源
     */
    private OrderSource source;
}
