package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.StationSource;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/03/15
 * 油站列表
 */
@Data
@TableName("tb_mall_order_ejy_station")
public class OrderEjyStation extends BaseEntity {

    /**
     * 油站id
     */
    String stationId;

    /**
     * 油站名称
     */
    String stationName;

    /**
     * 油站类型，1 中石油，2 中石化，3 壳牌，4 其他
     */
    int stationType;

    /**
     * 油站地址
     */
    String location;

    /**
     * 城市编号
     */
    int cityId;

    /**
     * 城市名称
     */
    String cityName;

    /**
     * 省份编号
     */
    int provinceId;

    /**
     * 省份
     */
    String provinceName;

    /**
     * 纬度，百度坐标系
     */
    String latitude;

    /**
     * 经度，百度坐标系
     */
    String longitude;

    /**
     * 油站组织
     */
    int stationOrgnization;

    /**
     * 电话
     */
    String phone;

    /**
     * 发票类型
     */
    int invoiceType;

    /**
     * 评分
     */
    String starNum;

    /**
     * 油站小图
     */
    String stationPic;

    /**
     * 油站大图
     */
    String stationBannerPic;

    /**
     * 地区
     */
    String district;

    /**
     * 地区
     */
    String isSupportStationInvoice;


    /**
     * 油站来源
     * 0：易加油
     * 1：团油
     */
    StationSource stationSource;
}
