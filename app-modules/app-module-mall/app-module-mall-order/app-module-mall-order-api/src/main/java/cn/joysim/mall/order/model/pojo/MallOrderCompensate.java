package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.converter.mybatis.list.StrListTypeHandler;
import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderCompensateAuditStatus;
import cn.joysim.mall.order.model.pojo.enums.OrderCompensateReasonType;
import cn.joysim.mall.order.model.pojo.enums.OrderReceiveTicketType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 *活动补偿管理
 * @TableName tb_mall_order_compensate
 */
@TableName(value ="tb_mall_order_compensate",autoResultMap = true)
@Data
public class MallOrderCompensate extends BaseEntity implements Serializable {



    /**
     * 接券手机号
     */
    private String receiveMobile;

    /**
     * 补偿skuID
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 产品面值
     */
    private BigDecimal skuFaceValue;

    /**
     * 补偿原因
     */
    private OrderCompensateReasonType reasonType;

    /**
     * 补偿详细说明
     */
    private String reasonDetail;

    /**
     * 活动统计区Id
     */
    private Long statisticsPageId;

    /**
     * 活动Id
     */
    private Long activityId;

    /**
     * 提交人
     */
    private String creator;


    /**
     * 提交人Id
     */
    private Long creatorId;

    /**
     * 审核状态
     */
    private OrderCompensateAuditStatus auditStatus;

    /**
     * 审核人
     */
    private String auditUser;


    /**
     * 审核人Id
     */
    private Long auditUserId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 上传截图证明
     */
    @TableField(typeHandler = StrListTypeHandler.class)
    private List<String> proof;

    /**
     * 短信通知
     */
    private String notifySmsContent;

    /**
     * 接券用户id
     */
    private Long userId;

    /**
     * 接券对象类型
     */
    private OrderReceiveTicketType receiveTicketType;

    /**
     * 子项目id
     */
    private Long subProjectId;

    /**
     * 订单领取有效天数
     */
    private Integer orderReceiveDays;
}
