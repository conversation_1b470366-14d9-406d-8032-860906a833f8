package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderPlatformType;
import lombok.Data;

import java.util.List;

/**
 * 订单数据传输对象
 *
 * <AUTHOR>
 * @date 2021/1/11
 */
@Data
public class OrderSaveDTO extends OrderSaveBaseDTO {

    /**
     * 实物订单
     */
    private OrderEntitySaveDTO entityOrder;
    /**
     * 充值订单
     */
    private OrderRechargeSaveDTO rechargeOrder;
    /**
     * 卡券订单
     */
    private OrderTicketSaveDTO ticketOrder;

    /**
     * 易加油订单
     */
    private OrderEjySaveDTO ejyOrder;

    /**
     * 订单平台分类
     */
    private OrderPlatformType orderPlatformType;

    /**
     * 银行兑换专区id
     */
    private Long exchangeId;

    /**
     * 总区的所有sku
     */
    private List<Long> arrondiSkuIds;

    /**
     * 团油订单
     */
    private OrderTySaveDTO orderTySaveDTO ;

    /**
     * 由{handSelByOrder}订单赠送的订单
     */
    private Long handSelByOrder;

    /**
     * 所属产品分区id
     */
    private Long arrondiId;

    /**
     * 产品分区的所有sku
     */
    private List<Long> productArrondiSkuIds;
}
