package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class PolymerizeOrderRecordSqlResultDTO {


    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    private String skuName;

    private Date orderTime;


    private OrderTypeEnum orderType;


    private Integer state;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

}
