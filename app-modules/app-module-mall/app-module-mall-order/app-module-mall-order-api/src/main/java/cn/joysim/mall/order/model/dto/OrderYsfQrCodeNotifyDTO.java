package cn.joysim.mall.order.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @version: 2.4.16
 * @author: linhs
 * @date: 2021/12/30
 * @desc: 云闪付支付回调
 */
@Data
public class OrderYsfQrCodeNotifyDTO implements Serializable {

    /**
     * 通知公共参数
     */
    private OrderYsfNotifyContentDTO content;

    private String accNo;
    private String appName;
    private String bankNm;
    private String currency;
    private String deviceInfo;
    private String discountAmt;
    private String instalTransInfo;
    private String mchntOrderId;
    private String merId;
    private String orderId;
    private String refundAt;
    private String settleAmt;
    private String settleDate;
    private String subTransTp;
    private String traceNo;
    private String traceTime;
    private String transAt;
    private String transTp;
    private String transSt;
    /**
     * 订单发送时间
     */
    private String txnTime;
//    private String orgnOrderId;
    //   private String refundCnt;


/*    private String subMerId;
    private String store;
    private String mchtDiscountAmt;*/
}
