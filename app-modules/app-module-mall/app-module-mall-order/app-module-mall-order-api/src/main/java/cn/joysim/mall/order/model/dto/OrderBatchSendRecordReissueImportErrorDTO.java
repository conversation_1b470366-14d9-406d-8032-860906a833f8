package cn.joysim.mall.order.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/1/7
 */
@Data
@Accessors(chain = true)
public class OrderBatchSendRecordReissueImportErrorDTO implements Serializable {


    /**
     * 发券记录ID
     */
    @ExcelProperty(value = "发券记录ID")
    private String sendRecordId;

    /**
     * 券产品条码
     */
    @ExcelProperty(value = "券产品条码")
    private String curBatchId;
    /**
     * 券产品条码
     */
    @ExcelProperty(value = "券产品条码(修改批次)")
    private String newBatchId;

    @ExcelProperty(value = "补发结果")
    private String errorMessage;



}
