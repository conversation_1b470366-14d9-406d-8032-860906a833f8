package cn.joysim.mall.order.model.dto;

import cn.joysim.mall.order.model.pojo.enums.OrderSource;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version: 1.0.0
 * @author: linhs
 * @date: 2022/7/14
 * @desc: H5回调参数
 */
@Data
@Accessors(chain = true)
public class OrderThirdPayNotifyParamDTO implements Serializable {
    /**
     * 支付单号
     */
    private Long paymentOrderId;

    /**
     * 订单来源
     */
    private OrderSource orderSource;
}
