package cn.joysim.mall.order.model.pojo;

import cn.joysim.common.model.pojo.BaseEntity;
import cn.joysim.mall.order.model.pojo.enums.OrderOliType;
import cn.joysim.mall.order.model.pojo.enums.TicketStateEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketSupplierEnum;
import cn.joysim.mall.order.model.pojo.enums.TicketTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @version: 2.5.2
 * @author: linhs
 * @date: 2022/3/14
 * @desc: 团油订单详情
 */
@Data
@TableName("tb_mall_order_ty_detail")
public class OrderTyDetail extends BaseEntity {

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 能链订单号
     */
    private String orderNo;
    /**
     * 油站名称
     */
    private String gasName;
    /**
     * 油站地址
     */
    private String gasAddress;
    /**
     * 订单所有状态 0-待⽀付 1-已⽀付 2-退款中 4-订单取消
     * 5-已退款 7-退款失败
     * * 当前状态 0 待⽀付
     */
    //private Integer orderStatus;
    /**
     * 下单时间
     */
    private String orderTime;
    /**
     * 油号
     */
    private String oilNo;
    /**
     * 油号名称
     */
    private String oilName;
    /**
     * 油枪号
     */
    private String gunNo;
    /**
     * 加油升数
     */
    private BigDecimal num;
    /**
     * 枪价
     */
    private BigDecimal gunPrice;
    /**
     * ⽤⼾实际加油单价
     */
    private BigDecimal userRealPrice;
    /**
     * 企业实际⽀付单价
     */
    private BigDecimal enterpriseRealPrice;
    /**
     * 订单⾦额---⽤⼾输⼊的加油⾦额
     */
    private BigDecimal totalOrderAmount;
    /**
     * ⽤⼾总实付⾦额
     */
    private BigDecimal totalUserRealAmout;
    /**
     * 企业总实扣⾦额
     */
    private BigDecimal totalEnterpriseRealAmount;
    /**
     * ⽤⼾总优惠⾦额
     */
    private BigDecimal totalUserDiscountAmount;
    /**
     * 企业总优惠⾦额
     */
    private BigDecimal totalEnterpriseDiscountAmount;
    /**
     * 团油员⼯⽤⼾Id
     */
    private String tyUserId;
    /**
     * 请求幂等流⽔号
     */
    private String uuid;
    /**
     * 授权码的透传的来源，⼤部分情况不需要，为空
     */
    private String source;
    /**
     * 业务接⼝类型（1=订单推送，2=⽀付推送，3=退款回
     * 告，4=标准收银台）⽤于同⼀url推送不同业务场景
     */
    private String bizType;

    /**
     * 跳转详情⻚的url（使⽤该url可以直接调整到详情⻚）
     */
    private String detailUrl;

    /**
     * 支付时间
     */
    private String payTime;
    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 报价快照id
     */
    private String snapshotPriceId;

    /**
     * 油站ID
     */
    private String gasId;

    /**
     * 班马手续费
     */
    private BigDecimal bmServiceFee;
}
