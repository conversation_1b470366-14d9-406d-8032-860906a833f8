package cn.joysim.mall.file.service.impl.asyncDownloadJob;


import cn.hutool.core.collection.CollUtil;
import cn.joysim.common.annotation.AsyncDownloadJobWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class AsyncDownloadJobHandlerContent  implements CommandLineRunner {


    @Resource
    private ApplicationContext applicationContext;
    private Map<String, Method> annotatedMethods = new HashMap<>();





    private void scanAndCacheAnnotatedMethods() {
        log.info("开始扫描异步下载任务方法");
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            Object bean = applicationContext.getBean(beanName);
            //判断beanName是否包含cn.joysim，不包含则跳过
            if (!bean.getClass().getName().contains("cn.joysim.mall")) {
                continue;
            }
            Class<?> clazz = bean.getClass();
            for (Method method : clazz.getDeclaredMethods()) {
                AsyncDownloadJobWorker annotation = AnnotationUtils.findAnnotation(method, AsyncDownloadJobWorker.class);
                if (annotation != null) {
                    // 这里假设方法名作为Map的key是唯一的
                    log.info("添加异步下载任务:{}",annotation.value());
                    annotatedMethods.put(annotation.value(), method);
                }
            }
        }
        log.info("完成扫描异步下载任务方法，共找到 {} 个方法", annotatedMethods.size());
    }

    public Map<String, Method> getAnnotatedMethods() {
        return annotatedMethods;
    }


    public Method getByName(String name) {
        Method method = null;
        method = annotatedMethods.get(name);
        if (Objects.isNull(method)) {
            if (CollUtil.isEmpty(annotatedMethods)) {
                scanAndCacheAnnotatedMethods();
            }
            method =annotatedMethods.get(name);
        }
        return method;
    }


    @Override
    public void run(String... args) throws Exception {
        log.info("应用启动扫描异步下载任务方法");
        scanAndCacheAnnotatedMethods();
    }
}
