common:
  application:
    name: jdff-mall
    # 是否开启debug模式
    debug-mode: true
    server:
      # http 端口（按需修改）
      port:  8888
    cors:
      # 是否启用CORS跨域配置
      enable: true
      # 允许哪些域名跨域调用
      domain: "*"
      # 配置哪些接口启用跨域
      path: "/**"
    datasource:
      # 数据库服务器IP
      ip:  ************
      # 端口
      port: 3306
      # 连接的数据库
      database:  jdff-mall
      # 用户名
      username:  root
      # 密码
      password:  mysqlPasdfdd!@#1ds23adfasFEItx
      worker-id: 1
      datacenter-id: 1
    redis:
      # 是否启用redis配置（admin-config：CacheConfig）
      enable: true
      # 存储的数据库（默认可选范围 0~15，按需修改）
      database: 3
      # redis服务器ip
      #host: ************
      # 端口
      host: ************
      port: 6379
      # 认证密码
      password: xypr0Pas1sw0rd123
      # 自定义redis group前缀（多应用共用一个redis的情形请修改，以防重名覆盖）
      key-prefix: "jdff-mall"
    token:
      # token有效期，单位：分钟
      timeout: 120
    captcha:
      required: false
      # 验证码超时时间 单位：分钟
      timeout: 10
      # 是否只能验证一次
      once-check: false
    job:
      appname: joysim
      # 是否启用定时任务
      enable: false
      # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      admin-address: http://127.0.0.1:8866/xxl-job-admin
      # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip:
      # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: -1
      # 执行器通讯TOKEN [选填]：非空时启用；
      access-token:
      # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      log-path: ${user.home}/logs/xxl-job
      # 执行器日志保存天数 [选填] ：值大于3时生效，启用执行器Log文件定期清理功能，否则不生效；
      log-retention-days: 7
    websocket:
      enable: true
      endpoint: /ws
      destination-prefix: /app
      broker: /topic
      origin: ${common.application.cors.domain}
    kafka:
      #servers: ************:9092
      servers: ************:9092
      group-id: test001
      # 配置kafka.prefix 设置值时修改kafka的监听主题，生产环境禁止启用，可能造成此前生产环境的消息无法消费
      #prefix: DEV_
    pwdtips:
      required: false
      # 提示时间，单位：月
      tipstime: 3
      # 是否强制修改密码
      forcechange: false
    file:
      storage-path: "Z:\\data\\file\\jdff-mall\\"
      file-path: "~/data/file/jdff-mall/file/"
      zip-path: "~/data/file/jdff-mall/zip/"
    alipay-mini:
      appId: "2021003127698367"
      decryptKey: "jZX6MF2mCHvElSvdZSMimg=="
      privateKey: "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCAvZCVmyNgXxWfxG3BtxJaQO+1BVII2qlz/P6trrd0OdOXQ3iZF64fCb1XCUcsUk75TrCSMSZukKgwYZW7kTo3+i9Cp8y2ijX3z5upwvYpdLX8Ygws4cG9xHj40OXnS2s+IZt6E/c2VYDKtY+VhEy34upw6uNPoULy2SvuCJFrxmQ70qSlGmXeoLrZybtX/Iwt81+E+SM71uHJeGVZPH29h/IHIBDNUtSCAx7tcUTnHr0jjEx2dB5N0mHrLZ4XCjvXF4BbcvvXTnbkzEwkhmS5j6RmpOAJISp0AoGaYt6fg3Hg789o5+anq2NXidWdRbLrqBZxaPM/r9NFK+a60saPAgMBAAECggEATbAXG/CufG1kozDp+sf67zPYcyhuxKEPOMsm9LBGTIDIyrMQh3HexSQfefJDXN+egJuXueCnDHmGxQteS/3rX/Q9qL+RrWXiA1To3pyj54hieZbBOiRRwa5aQevtkJHWeXvWUujReZxHSAwgRV283gq05scXvOnXeKzM8auB9cK7ghrmPOitV4o+kzluxOoaOAmNKxgOmhyUixG9Z5tye6AEBtdueLmswIKR/YsiO0yMfnV2ULuf7EcJWt2E7s352pi+wZKNA3s3YXGAH/U2EdYlaorF/FNzfNalydr1jCnnhPJs9f1J0e9Avmw4qgy1+H0+pTa66SoaqWxQQ0nZ4QKBgQC38UJH38tBdg0PmbpzN+uftH9ob54snlrCfEbGy7cVMc/r6CKwWA4amkacTsL93PQqt96N4F4XGKww5QlvjQ3fCbncR/e0JCGHb1mMRlhcddW6t+uzrIfsAfhVPksa1PkoSnpwxiFqlRoSGfehPEXvE70eV5zkH2/iTQncAXJXQwKBgQCzLFuKDuCDt7uvaNCH9hAMip8K6bfoII70c5qIdS00Z3P6/qClnbzVFAsAGDaaecwQuygBFxICs/OwvWP038Zl6aZJaUFXdJDWGw0wSLgatMw5RZkkB7aiGqoRoqUxqLpfPcon0Yx16kWXD91MjEG2msO/WqzOUUikbxBzLWngxQKBgEDkw755rMqmbEsxr/7QHreURVLB9irFnz13/LxLyIaYsUMH/Zz7I9as0PZ6Lv7DAf1EKycgnfFGChGh7CdHuzoRw1l8pl4OVSnj1dLQSoIsLEAYfPZBix5vP35LYNHRiyl0xSeeLtHcUntKX/upOP70lsbqn/Y3RRCwnybX98/jAoGAEr22CXfK5hwmxo85GK+VOCqbviF6Og6FxW3+IP3Yr52OlRdjKE+ae9Mv31+o/nl07d8VrR+Do8K836S/utJZRzmsSgt3aTVpHOwRchZ5I1n77pWoEgu4f7Y4ScXwb4RNkj69UXLqtT6+7rEbHbGiAf6oMPDEGISKC6EOYG3EoF0CgYBvA6qOcvyBCf2nheHnbw9XOVKD+zwRtbne3ZT91wLx3yzqIOsAJBeJgawmHGaTFHjE++g2EPYCK2ssjinQ5QqBKXXKsZhto1yWHuhYaepiRLqUfK/k8NOnR+DueQ60lhWFs38lnuZsQjEbs4a8VzK7Z63itJeB68okijUEmSarfQ=="
      publicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj8GKAj//3UF85D67UE0jBbMxBN32eeiK93mqR454gAJxwsxf0XCc15uYUIw3n6cIcPoq5gAbcdOuD5Dirc2qLRuyY0NmhRfriXFeqUfS1EqnkCgjnQdHXbQkJ6Vxqd/NeHT/Z2TB26lUgA6TpLFQkViiI+aLQAt8UBlN5b311nmaiqMBQpgVNO2CfRBmiCmSRb99ffCcvaf+V+u5thAvxxrOGS60Bh6FS2OXBgLjmpVTr2WuWj43B2tS1o31Xe2sAL4dV7rcS0G0vGlMCqM8SyveOtsHt7fr9htAtBZ5ERJSwXKaDU1P4SaXdO+DIHdj982dEkPoVAUP4+KkgRHseQIDAQAB"
    #LDAP登录集成
    ldap:
      enable: true
      url: ldaps://ldap-server.joysim.cn
      # 目录的连接器信息与配置信息里可查询获取
      domain: ou=People,dc=uia,dc=com
    feign:
      url:
        wx-crawler: http://*************:8017
    real-profile: dev
#spring:
#  kafka:
#    producer:
#      properties:
# 配置interceptor.classes设置值时修改kafka的发送主题，生产环境禁止启用，可能造成发送的队列错误
#        interceptor.classes: cn.joysim.interceptor.KafkaProducerInterceptor