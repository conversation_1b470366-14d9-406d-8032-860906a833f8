package ${package.Controller};


import org.springframework.web.bind.annotation.RequestMapping;
#if(${restControllerStyle})
import org.springframework.web.bind.annotation.RestController;
#else
import org.springframework.stereotype.Controller;
#end
#if(${superControllerClassPackage})
import ${superControllerClassPackage};
#end
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import cn.joysim.common.annotation.Permission;
import org.springframework.web.bind.annotation.RequestBody;
import cn.joysim.common.model.query.PageQuery;
import cn.joysim.common.model.vo.BaseRequest;
import cn.joysim.common.model.vo.BaseResponse;
import ${basePackage}.${package.ModuleName}.model.dto.${entity}Dto;
import ${basePackage}.${package.ModuleName}.model.query.${entity}Query;
import ${package.Service}.${table.serviceName};
import javax.validation.Valid;
import cn.joysim.common.utils.BeanUtil;
import cn.joysim.common.controller.request.PrimaryKeyRequest;
import java.util.Date;
import ${package.Entity}.${entity};
import ${basePackage}.${package.ModuleName}.model.query.${entity}Query;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * $!{table.comment} 前端控制器
 *
 * <AUTHOR>
 * @date ${date}
 */
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
@RequestMapping("${controllerMappingPrefix}#if(${package.ModuleName})/${package.ModuleName}#end/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityName}#end")
@Slf4j
#if(${kotlin})
class ${table.controllerName}#if(${superControllerClass}) : ${superControllerClass}()#end

#else
#if(${superControllerClass})
public class ${table.controllerName} extends ${superControllerClass} {
#else
public class ${table.controllerName} {
#end

    @Resource
    private ${table.serviceName} ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)};

    /**
     * $!{table.comment} 数据列表
     * @param request
     * @return
     */
    @RequestMapping("/page")
    @Permission(value = "api:${controllerMappingPrefix}:${package.ModuleName}:${table.entityPath}:page")
    public BaseResponse<${entity}Dto> page(@RequestBody BaseRequest<PageQuery<${entity}, ${entity}Query>> request){
        IPage<${entity}> page = (IPage)request.getData();
        ${entity}Query params = (${entity}Query)((PageQuery)request.getData()).getParams();
        this.${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.page(page, params);
        return this.successResponse(page);
    }

    /**
     * 新增$!{table.comment}
     * @param request
     * @return
     */
    @RequestMapping("/save")
    @Permission(value = "api:${controllerMappingPrefix}:${package.ModuleName}:${table.entityPath}:save")
    public BaseResponse<Boolean> save(@Valid @RequestBody BaseRequest<${entity}> request){
        ${entity} dto = BeanUtil.toBean(request.getData(), ${entity}.class);
        boolean result = ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.save(dto);
        return baseResponse(result);
    }

    /**
     * 删除操作 $!{table.comment}
     * @param request
     * @return
     */
    @RequestMapping("/delete")
    @Permission(value = "api:${controllerMappingPrefix}:${package.ModuleName}:${table.entityPath}:delete")
    public BaseResponse<Boolean> delete(@Valid @RequestBody BaseRequest<PrimaryKeyRequest> request){
        return baseResponse(${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.removeById(request.getData().getId()));
    }

    /**
     * 通过ID获取$!{table.comment}
     * @param request
     * @return
     */
    @RequestMapping("/getById")
    @Permission(value = "api:${controllerMappingPrefix}:${package.ModuleName}:${table.entityPath}:getById")
    public BaseResponse<${entity}Dto> getById(@Valid @RequestBody BaseRequest<PrimaryKeyRequest> request){
        return successResponse(${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.getById(request.getData().getId()));
    }

    /**
     * 修改$!{table.comment}
     * @param request
     * @return
     */
    @RequestMapping("/update")
    @Permission(value = "api:${controllerMappingPrefix}:${package.ModuleName}:${table.entityPath}:update")
    public BaseResponse<Boolean> update(@Valid @RequestBody BaseRequest<${entity}> request){
        ${entity} ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)} = BeanUtil.toBean(request.getData(), ${entity}.class);
        ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}.setUpdateTime(new Date());
        boolean result = ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.updateById(${entity.substring(0, 1).toLowerCase()}${entity.substring(1)});
        return baseResponse(result);
    }


}

#end