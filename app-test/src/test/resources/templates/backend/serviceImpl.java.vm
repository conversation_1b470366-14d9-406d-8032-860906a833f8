package ${package.ServiceImpl};

import ${basePackage}.${module}.model.pojo.${entity};
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.joysim.mall.${module}.mapper.${table.mapperName};
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @date ${date}
 */
@Service
@Slf4j
public class ${table.serviceImplName} extends ServiceImpl< ${table.mapperName},  ${entity}> {


}

