<template>
  <div class="app-container">
    <data-table
      ref="customTable"
      :request="page"
      :search-form="searchForm"
    >
      <template slot="search">
      	#foreach($field in ${table.fields})
	  this.formData.${field.propertyName} = result.data.${field.propertyName};
	  <el-form-item prop="${field.propertyName}">
          <el-input
            v-model="searchForm.${field.propertyName}"
            type="text"
            placeholder="${field.comment}"
            />
          </el-form-item>
	#end
      </template>
      <template slot="function">
        <el-button
          v-permission="['function:${package.ModuleName}:${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}:add']"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
        >新增</el-button>
      </template>
      <template slot="tableColumns">
        <el-table-column
          prop="id"
          label="ID"
          :show-overflow-tooltip="true"
        />
        #foreach($field in ${table.fields})
	  <el-table-column
            prop="${field.propertyName}"
            label="${field.comment}"
          />
	#end
        <el-table-column
          prop="action"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button-group>
              <el-button
                v-permission="['function:${package.ModuleName}:${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}:edit']"
                type="primary"
                icon="el-icon-edit"
                circle
                @click.native.prevent="edit(scope.$index, scope.row)"
              />
              <el-button
                v-permission="['function:${package.ModuleName}:${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}:remove']"
                type="danger"
                icon="el-icon-delete"
                circle
                @click.native.prevent="remove(scope.$index, scope.row)"
              />
            </el-button-group>
          </template>
        </el-table-column>
      </template>
    </data-table>
    <el-dialog
      :title="customFormInitOption.title"
      :visible.sync="customFormInitOption.showModal"
      :close-on-click-modal="false"
    >
      <${entity}Form
        ref="customForm"
        :action="customFormInitOption.action"
      />
      <div slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit"
        >保存</el-button>
        <el-button
          v-if="customFormInitOption.action === 'add'"
          type="danger"
          @click="reset"
        >重置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DataTable from '@/components/datatable/data-table';
import ${entity}Form from '@/views/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/form';
import { remove, page } from '@/api/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}';

export default {
  components: {
    DataTable,
    ${entity}Form
  },
  data() {
    return {
      page: page,
      searchForm: {
        #foreach($field in ${table.fields})
	  ${field.propertyName}: null,
        #end
      },
      customFormInitOption: {
        title: '',
        action: '',
        showModal: false
      }
    };
  },
  methods: {
    handleAdd() {
      this.customFormInitOption.title = '新增配置';
      this.customFormInitOption.action = 'add';
      this.customFormInitOption.showModal = true;
      this.$nextTick(() => {
        this.$refs.customForm.reset();
      });
    },
    edit(index, row) {
      this.customFormInitOption.title = '编辑配置';
      this.customFormInitOption.action = 'edit';
      this.customFormInitOption.showModal = true;
      this.$nextTick(() => {
        this.$refs
        .customForm.getInfo(row.id)
      });
    },
    remove(index, row) {
      this.$confirm('确定删除名称为【' + row.keyName + '】的配置吗？', '请确认', {
        type: 'warning'
      }).then(() => {
        remove(row.id).then(response => {
          const result = response.data;
          let title, type, text;
          if (result.code === 0) {
            title = '成功';
            type = 'success';
          } else {
            title = result.code;
            text = result.msg;
            type = 'error';
          }
          this.$notify({
            title: title,
            message: text,
            type: type
          });
          this.$refs.customTable.query();
        });
      }).catch(() => { });
    },
    handleSubmit() {
      this.$refs.customForm.handleSubmit();
    },
    reset() {
      this.$refs.customForm.sonFormData = [];
      this.$refs.customForm.count = 0;
      this.$refs.customForm.reset();
    }
  }
};
</script>
