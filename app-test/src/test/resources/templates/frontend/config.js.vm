import request from '@/utils/request';

/**
 * 分页查询$!{table.comment}
 * @param {*} pageParam
 */
export const page = pageParam => {
  return request({
    url: '/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/page',
    data: pageParam,
    method: 'post'
  });
};

/**
 * 新增$!{table.comment}
 * @param {*} ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}
 */
export const save = ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)} => {
  return request({
    url: '/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/save',
    data: ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)},
    method: 'post'
  });
};

/**
 * 更新$!{table.comment}
 * @param {} ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}
 */
export const update = ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)} => {
  return request({
    url: '/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/update',
    data: ${entity.substring(0, 1).toLowerCase()}${entity.substring(1)},
    method: 'post'
  });
};

/**
 * 获取$!{table.comment}
 * @param {*} id
 */
export const get = param => {
  return request({
    url: '/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/get',
    data: param,
    method: 'post'
  });
};

/**
 * 删除$!{table.comment}
 * @param {*} id
 */
export const remove = id => {
  return request({
    url: '/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}/delete',
    data: {
      id: id
    },
    method: 'post'
  });
};


