<template>
  <el-form ref="${entity}Form" :model="formData" :rules="formRules" label-width="90px">
    <el-form-item v-if="action === 'edit'" prop="id">
      <input type="hidden" :model="formData.id">
    </el-form-item>
    #foreach($field in ${table.fields})
	<el-form-item label="${field.comment}" prop="${field.propertyName}">
		<el-input v-model="formData.${field.propertyName}" type="text" placeholder="请输入${field.comment}" />
	</el-form-item>
    #end
  </el-form>
</template>
<script>
import { get, save, update } from '@/api/${package.ModuleName}/${entity.substring(0, 1).toLowerCase()}${entity.substring(1)}';

export default {
  name: '${entity}FormDialog',
  props: {
    action: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      formRules: {
        #foreach($field in ${table.fields})
        ${field.propertyName}: [
          { required: true, message: '${field.comment}不能为空！', trigger: 'blur' }
        ],
        #end
      },
      formData: {
      #foreach($field in ${table.fields})
	    ${field.propertyName}: null,
      #end
      }
    };
  },
  methods: {
    getInfo(id) {
      get({ id: id }).then(response => {
        const result = response.data;
        if (result.code === 0) {
        #foreach($field in ${table.fields})
          this.formData.${field.propertyName} = result.data.${field.propertyName};
        #end
        } else {
          this.$notify
          .error({
          title:response.data.code,
          message:response.data.msg
          });
        }
      });
    },
    handleSubmit() {
      this.$refs.${entity}Form.validate(valid => {
        if (valid) {
          if (this.action === 'add') {
            const params = { ...this.formData };
            save(params).then(response => {
              this.handleResult(response.data);
            });
          } else if (this.action === 'edit') {
            update(this.formData).then(response => {
              this.handleResult(response.data);
            });
          }
        }
      });
    },
    handleResult(obj) {
      if (obj.code === 0) {
        this.$notify({
          title: '提示',
          message: obj.msg,
          type: 'success'
        });

        this.$parent.$parent.customFormInitOption.showModal = false;
        this.$parent.$parent.$refs.customTable.query();
        this.reset();
      } else {
        this.$notify
        .error({
          title:obj.code,
          message:obj.msg
        });
      }
    },
    reset() {
      this.formData.sonFormData = [];
      this.count = 0;
      this.$refs.${entity}Form.resetFields();
    }
  }
};
</script>
