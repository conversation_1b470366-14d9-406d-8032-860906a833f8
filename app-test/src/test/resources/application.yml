
spring:
  application:
    name: ${common.application.name}
  profiles:
    active:  dev

  http:
    encoding:
      charset:  UTF-8
      enabled:  true
      force:  true
      force-request:  true
      force-response:  true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    deserialization:
      #允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
    parser:
      #允许出现特殊字符和转义符
      allow_unquoted_control_chars: true
      #允许出现单引号
      allow_single_quotes: true

  mvc:
    throw-exception-if-no-handler-found:  true
    hiddenmethod:
      filter:
        enabled: true

  resources:
    add-mappings:  false

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name:  com.mysql.cj.jdbc.Driver
    url:  jdbc:mysql://${common.application.datasource.ip}:${common.application.datasource.port}/${common.application.datasource.database}?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
    username:  ${common.application.datasource.username}
    password:  ${common.application.datasource.password}
    hikari:
      minimum-idle: 20
      maximum-pool-size: 200
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    database: ${common.application.redis.database}
    host: ${common.application.redis.host}
    port: ${common.application.redis.port}
    password: ${common.application.redis.password}
    timeout: 3000
  kafka:
    bootstrap-servers: ${common.application.kafka.servers}
    consumer:
      group-id: ${common.application.kafka.group-id}
  servlet:
    multipart:
      max-file-size: 100MB
  lifecycle:
    timeout-per-shutdown-phase: 30s
  cloud:
    alicloud:
      oss:
        endpoint: https://oss-cn-hangzhou.aliyuncs.com
      access-key: LTAI5tKJV2FJ9CgTJCsjCmY1
      secret-key: ******************************

server:
  port:  ${common.application.server.port}
  servlet:
    context-path: /api
  tomcat:
    max-threads: 1000
    max-connections: 5000
    accept-count: 2000
  shutdown: graceful
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔\
  type-aliases-package: cn.joysim.*.model.pojo;cn.joysim.*.*.model.pojo
  type-enums-package: cn.joysim.*.model.pojo.enums;cn.joysim.*.*.model.pojo.enums
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: ${common.application.debug-mode}
    #数据库大写下划线转换
    #capital-mode: true
    #序列接口实现类配置,不在推荐使用此方式进行配置,请使用自定义bean注入
    key-generator: com.baomidou.mybatisplus.incrementer.H2KeyGenerator
    #逻辑删除配置（下面3个配置）
    logic-delete-value: 1
    logic-not-delete-value: 0
    # SQL 解析缓存，开启后多租户 @SqlParser 注解生效
    sql-parser-cache: true
    datacenter-id: ${common.application.datasource.datacenter-id}
    worker-id: ${common.application.datasource.worker-id}
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 日志配置
logging:
  config:  classpath:log4j2.xml
